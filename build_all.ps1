# TradingView Master Build Script
# Tạo tất cả các loại build: executable + installer
# Educational purposes only

param(
    [string]$AppName = "TradingView",
    [string]$AppVersion = "1.0.0",
    [switch]$BuildExecutable = $true,
    [switch]$BuildInstaller = $true,
    [switch]$SkipUpx = $false,
    [switch]$Verbose = $false
)

function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Step { param($Message) Write-Host $Message -ForegroundColor Blue }
function Write-Header { param($Message) Write-Host $Message -ForegroundColor Magenta }

Clear-Host
Write-Header "========================================================"
Write-Header "    TradingView Master Build System v2.0"
Write-Header "    Tạo executable + installer tự động"
Write-Header "========================================================"
Write-Host ""

$StartTime = Get-Date
$ProjectDir = Get-Location
$LogFile = Join-Path $ProjectDir "build_log.txt"

# Initialize log
"TradingView Build Log - $(Get-Date)" | Out-File -FilePath $LogFile -Encoding UTF8
"========================================" | Out-File -FilePath $LogFile -Append -Encoding UTF8

function Write-Log {
    param($Message)
    $Timestamp = Get-Date -Format "HH:mm:ss"
    "[$Timestamp] $Message" | Out-File -FilePath $LogFile -Append -Encoding UTF8
}

Write-Step "🔍 Checking prerequisites..."
Write-Log "Starting build process"

# Check required files
$RequiredFiles = @("main.py", "wallet.py", "social.py", "requirements.txt")
$MissingFiles = @()

foreach ($file in $RequiredFiles) {
    if (Test-Path $file) {
        Write-Success "✓ Found: $file"
        Write-Log "Found required file: $file"
    } else {
        Write-Warning "⚠ Missing: $file"
        Write-Log "Missing required file: $file"
        $MissingFiles += $file
    }
}

if ($MissingFiles.Count -gt 0) {
    Write-Error "❌ Missing required files. Cannot continue."
    Write-Log "Build failed: Missing required files"
    exit 1
}

# Check optional files
if (Test-Path "logo.ico") {
    Write-Success "✓ Icon file found"
    Write-Log "Icon file available"
} else {
    Write-Warning "⚠ Icon file not found (logo.ico)"
    Write-Log "Icon file not found"
}

Write-Step "`n📊 Build Configuration:"
Write-Info "  App Name: $AppName"
Write-Info "  Version: $AppVersion"
Write-Info "  Build Executable: $BuildExecutable"
Write-Info "  Build Installer: $BuildInstaller"
Write-Info "  Skip UPX: $SkipUpx"
Write-Info "  Verbose: $Verbose"

Write-Log "Build configuration: AppName=$AppName, Version=$AppVersion, BuildExecutable=$BuildExecutable, BuildInstaller=$BuildInstaller"

$BuildResults = @{
    Executable = $false
    Installer = $false
    Errors = @()
}

# Build Executable
if ($BuildExecutable) {
    Write-Header "`n🔨 PHASE 1: Building Executable"
    Write-Step "Running advanced build script..."
    Write-Log "Starting executable build"
    
    try {
        if (Test-Path "build_advanced.ps1") {
            $BuildArgs = @()
            if ($SkipUpx) { $BuildArgs += "-SkipUpx" }
            if ($Verbose) { $BuildArgs += "-Verbose" }
            
            & .\build_advanced.ps1 @BuildArgs
            
            # Check if executable was created
            $ExePath = Join-Path "dist" "$AppName.exe"
            if (Test-Path $ExePath) {
                $ExeSize = [math]::Round((Get-Item $ExePath).Length / 1MB, 2)
                Write-Success "✓ Executable build successful!"
                Write-Info "  Location: $ExePath"
                Write-Info "  Size: $ExeSize MB"
                Write-Log "Executable build successful: $ExePath ($ExeSize MB)"
                $BuildResults.Executable = $true
            } else {
                throw "Executable not found after build"
            }
        } else {
            throw "build_advanced.ps1 not found"
        }
    } catch {
        Write-Error "❌ Executable build failed: $($_.Exception.Message)"
        Write-Log "Executable build failed: $($_.Exception.Message)"
        $BuildResults.Errors += "Executable build failed"
    }
} else {
    Write-Info "Skipping executable build"
    Write-Log "Executable build skipped"
}

# Build Installer
if ($BuildInstaller -and $BuildResults.Executable) {
    Write-Header "`n📦 PHASE 2: Building Installer"
    Write-Step "Running simple setup builder..."
    Write-Log "Starting installer build"
    
    try {
        if (Test-Path "build_simple_setup.ps1") {
            $InstallerArgs = @(
                "-AppName", $AppName,
                "-AppVersion", $AppVersion
            )
            
            & .\build_simple_setup.ps1 @InstallerArgs
            
            # Check if installer was created
            $SetupFiles = @(
                "setup_output/${AppName}_Setup_v${AppVersion}.ps1",
                "setup_output/${AppName}_Setup_v${AppVersion}.bat",
                "setup_output/silent_install.ps1"
            )
            
            $InstallerCreated = $false
            foreach ($setupFile in $SetupFiles) {
                if (Test-Path $setupFile) {
                    $SetupSize = [math]::Round((Get-Item $setupFile).Length / 1KB, 1)
                    Write-Success "✓ Created: $(Split-Path $setupFile -Leaf) ($SetupSize KB)"
                    Write-Log "Installer created: $setupFile ($SetupSize KB)"
                    $InstallerCreated = $true
                }
            }
            
            if ($InstallerCreated) {
                Write-Success "✓ Installer build successful!"
                Write-Log "Installer build successful"
                $BuildResults.Installer = $true
            } else {
                throw "No installer files created"
            }
        } else {
            throw "build_simple_setup.ps1 not found"
        }
    } catch {
        Write-Error "❌ Installer build failed: $($_.Exception.Message)"
        Write-Log "Installer build failed: $($_.Exception.Message)"
        $BuildResults.Errors += "Installer build failed"
    }
} elseif ($BuildInstaller -and -not $BuildResults.Executable) {
    Write-Warning "⚠ Skipping installer build (executable build failed)"
    Write-Log "Installer build skipped: executable build failed"
} else {
    Write-Info "Skipping installer build"
    Write-Log "Installer build skipped"
}

# Build Summary
$EndTime = Get-Date
$Duration = $EndTime - $StartTime

Write-Header "`n📋 BUILD SUMMARY"
Write-Header "========================================================"

Write-Step "⏱️  Build Duration: $($Duration.ToString('mm\:ss'))"
Write-Log "Build completed in $($Duration.ToString('mm\:ss'))"

if ($BuildResults.Executable) {
    Write-Success "✅ Executable: SUCCESS"
} else {
    Write-Error "❌ Executable: FAILED"
}

if ($BuildResults.Installer) {
    Write-Success "✅ Installer: SUCCESS"
} else {
    Write-Error "❌ Installer: FAILED"
}

if ($BuildResults.Errors.Count -gt 0) {
    Write-Warning "`n⚠️  Errors encountered:"
    foreach ($error in $BuildResults.Errors) {
        Write-Warning "  • $error"
    }
}

Write-Step "`n📁 Output Locations:"
if (Test-Path "dist") {
    Write-Info "  Executable: dist/"
    Get-ChildItem "dist" -Filter "*.exe" | ForEach-Object {
        $size = [math]::Round($_.Length / 1MB, 2)
        Write-Host "    📄 $($_.Name) ($size MB)" -ForegroundColor White
    }
}

if (Test-Path "setup_output") {
    Write-Info "  Installer: setup_output/"
    Get-ChildItem "setup_output" | ForEach-Object {
        if (-not $_.PSIsContainer) {
            $size = [math]::Round($_.Length / 1KB, 1)
            Write-Host "    📄 $($_.Name) ($size KB)" -ForegroundColor White
        } else {
            Write-Host "    📁 $($_.Name)/" -ForegroundColor White
        }
    }
}

Write-Step "`n🚀 Next Steps:"
if ($BuildResults.Executable -and $BuildResults.Installer) {
    Write-Success "✅ Build completed successfully!"
    Write-Info "  1. Test the executable: dist/$AppName.exe"
    Write-Info "  2. Test the installer: setup_output/${AppName}_Setup_v${AppVersion}.bat"
    Write-Info "  3. For silent install: setup_output/silent_install.ps1"
    Write-Log "Build completed successfully"
} elseif ($BuildResults.Executable) {
    Write-Warning "⚠️  Executable built, but installer failed"
    Write-Info "  1. Test the executable: dist/$AppName.exe"
    Write-Info "  2. Check build log for installer errors"
    Write-Log "Build partially completed: executable only"
} else {
    Write-Error "❌ Build failed"
    Write-Info "  1. Check build log: build_log.txt"
    Write-Info "  2. Verify all required files are present"
    Write-Info "  3. Check Python and dependencies"
    Write-Log "Build failed"
}

Write-Header "`n⚠️  Important Reminders:"
Write-Host "• Test in safe environment before distribution" -ForegroundColor Yellow
Write-Host "• Some antivirus may flag the files" -ForegroundColor Yellow
Write-Host "• This is for educational purposes only" -ForegroundColor Yellow
Write-Host "• Check build_log.txt for detailed information" -ForegroundColor Yellow

Write-Log "Build process completed"
Write-Host "`nBuild log saved to: build_log.txt" -ForegroundColor Cyan

Read-Host "`nPress Enter to exit"
