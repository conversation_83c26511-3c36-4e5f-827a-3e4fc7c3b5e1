(['D:\\tool\\python\\bot4\\main.py'],
 ['D:\\tool\\python\\bot4'],
 ['win32con',
  'Crypto',
  'telebot',
  'wallet',
  'social',
  'pyautogui',
  'PIL',
  'browser_cookie3',
  'browser_history',
  'getmac',
  'psutil',
  'cpuinfo',
  'pycountry'],
 [('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['tkinter', 'matplotlib', 'numpy', '__main__'],
 [],
 False,
 {},
 2,
 [],
 [],
 '3.11.0 (main, Oct 24 2022, 18:26:48) [MSC v.1933 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'D:\\tool\\python\\bot4\\main.py', 'PYSOURCE-2')],
 [('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\threading.py',
   'PYMODULE-2'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_threading_local.py',
   'PYMODULE-2'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextlib.py',
   'PYMODULE-2'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\signal.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\client.py',
   'PYMODULE-2'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE-2'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gzip.py',
   'PYMODULE-2'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\argparse.py',
   'PYMODULE-2'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\textwrap.py',
   'PYMODULE-2'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\copy.py',
   'PYMODULE-2'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gettext.py',
   'PYMODULE-2'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compression.py',
   'PYMODULE-2'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\__init__.py',
   'PYMODULE-2'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE-2'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\handler.py',
   'PYMODULE-2'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE-2'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE-2'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py',
   'PYMODULE-2'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ssl.py',
   'PYMODULE-2'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\calendar.py',
   'PYMODULE-2'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\__init__.py',
   'PYMODULE-2'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\message.py',
   'PYMODULE-2'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\policy.py',
   'PYMODULE-2'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\generator.py',
   'PYMODULE-2'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\charset.py',
   'PYMODULE-2'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\header.py',
   'PYMODULE-2'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\errors.py',
   'PYMODULE-2'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\utils.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE-2'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\quopri.py',
   'PYMODULE-2'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getopt.py',
   'PYMODULE-2'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\parser.py',
   'PYMODULE-2'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\decimal.py',
   'PYMODULE-2'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE-2'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextvars.py',
   'PYMODULE-2'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\numbers.py',
   'PYMODULE-2'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\datetime.py',
   'PYMODULE-2'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_strptime.py',
   'PYMODULE-2'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\base64.py',
   'PYMODULE-2'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hmac.py',
   'PYMODULE-2'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hashlib.py',
   'PYMODULE-2'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\struct.py',
   'PYMODULE-2'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socket.py',
   'PYMODULE-2'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\selectors.py',
   'PYMODULE-2'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE-2'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pickle.py',
   'PYMODULE-2'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pprint.py',
   'PYMODULE-2'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dataclasses.py',
   'PYMODULE-2'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\inspect.py',
   'PYMODULE-2'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\typing.py',
   'PYMODULE-2'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipfile.py',
   'PYMODULE-2'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\py_compile.py',
   'PYMODULE-2'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\lzma.py',
   'PYMODULE-2'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bz2.py',
   'PYMODULE-2'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pathlib.py',
   'PYMODULE-2'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fnmatch.py',
   'PYMODULE-2'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\csv.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\token.py',
   'PYMODULE-2'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tokenize.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dis.py',
   'PYMODULE-2'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\opcode.py',
   'PYMODULE-2'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ast.py',
   'PYMODULE-2'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE-2'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bisect.py',
   'PYMODULE-2'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE-2'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\util.py',
   'PYMODULE-2'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_aix.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE-2'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE-2'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE-2'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\wintypes.py',
   'PYMODULE-2'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\queue.py',
   'PYMODULE-2'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\secrets.py',
   'PYMODULE-2'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\runpy.py',
   'PYMODULE-2'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pkgutil.py',
   'PYMODULE-2'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipimport.py',
   'PYMODULE-2'),
  ('pycountry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\__init__.py',
   'PYMODULE-2'),
  ('pycountry.db',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\db.py',
   'PYMODULE-2'),
  ('browser_history',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\browser_history\\__init__.py',
   'PYMODULE-2'),
  ('browser_history.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\browser_history\\utils.py',
   'PYMODULE-2'),
  ('browser_history.browsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\browser_history\\browsers.py',
   'PYMODULE-2'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\__init__.py',
   'PYMODULE-2'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\dump.py',
   'PYMODULE-2'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE-2'),
  ('browser_history.generic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\browser_history\\generic.py',
   'PYMODULE-2'),
  ('browser_cookie3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\browser_cookie3\\__init__.py',
   'PYMODULE-2'),
  ('Cryptodome.Util.Padding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\Padding.py',
   'PYMODULE-2'),
  ('Cryptodome.Util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\__init__.py',
   'PYMODULE-2'),
  ('Cryptodome.Util._cpu_features',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\_cpu_features.py',
   'PYMODULE-2'),
  ('Cryptodome.Util._raw_api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\_raw_api.py',
   'PYMODULE-2'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\imp.py',
   'PYMODULE-2'),
  ('Cryptodome.Util._file_system',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\_file_system.py',
   'PYMODULE-2'),
  ('Cryptodome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\__init__.py',
   'PYMODULE-2'),
  ('Cryptodome.Util.py3compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\py3compat.py',
   'PYMODULE-2'),
  ('Cryptodome.Protocol.KDF',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Protocol\\KDF.py',
   'PYMODULE-2'),
  ('Cryptodome.Protocol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Protocol\\__init__.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._EKSBlowfish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_EKSBlowfish.py',
   'PYMODULE-2'),
  ('Cryptodome.Util.number',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\number.py',
   'PYMODULE-2'),
  ('Cryptodome.Random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Random\\__init__.py',
   'PYMODULE-2'),
  ('Cryptodome.Util.strxor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\strxor.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.BLAKE2s',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\BLAKE2s.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.CMAC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\CMAC.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.HMAC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\HMAC.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.MD5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\MD5.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA256.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA1.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\__init__.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA3_512',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA3_512.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.keccak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\keccak.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA3_384',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA3_384.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA3_256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA3_256.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA3_224',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA3_224.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA512',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA512.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA384',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA384.py',
   'PYMODULE-2'),
  ('Cryptodome.Hash.SHA224',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\SHA224.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher.AES',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\AES.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\__init__.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_kwp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_kwp.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_kw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_kw.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_ocb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_ocb.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_gcm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_gcm.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_siv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_siv.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_ccm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_ccm.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_eax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_eax.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_openpgp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_openpgp.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_ctr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_ctr.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_ofb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_ofb.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_cfb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_cfb.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_cbc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_cbc.py',
   'PYMODULE-2'),
  ('Cryptodome.Cipher._mode_ecb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_mode_ecb.py',
   'PYMODULE-2'),
  ('lz4.block',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4\\block\\__init__.py',
   'PYMODULE-2'),
  ('lz4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4\\__init__.py',
   'PYMODULE-2'),
  ('lz4.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4\\version.py',
   'PYMODULE-2'),
  ('shadowcopy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\shadowcopy\\__init__.py',
   'PYMODULE-2'),
  ('shadowcopy.shadow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\shadowcopy\\shadow.py',
   'PYMODULE-2'),
  ('shadowcopy.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\shadowcopy\\exceptions.py',
   'PYMODULE-2'),
  ('wmi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\wmi.py',
   'PYMODULE-2'),
  ('pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE-2'),
  ('pywin32_system32', '-', 'PYMODULE-2'),
  ('win32com.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE-2'),
  ('winerror',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE-2'),
  ('win32com.client.makepy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE-2'),
  ('pywin.dialogs.status',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE-2'),
  ('pywin.mfc.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE-2'),
  ('pywin.mfc.object',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE-2'),
  ('pywin.mfc.dialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE-2'),
  ('pywin.mfc.window',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE-2'),
  ('pywin.mfc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE-2'),
  ('pywin.dialogs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE-2'),
  ('pywin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE-2'),
  ('win32com.client.selecttlb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE-2'),
  ('pywin.dialogs.list',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE-2'),
  ('commctrl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE-2'),
  ('win32com.client.genpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE-2'),
  ('win32com.client.gencache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE-2'),
  ('win32com.client.CLSIDToClass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE-2'),
  ('win32com.client.dynamic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE-2'),
  ('win32com.client.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE-2'),
  ('win32com.client.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE-2'),
  ('win32com',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE-2'),
  ('win32com.server.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE-2'),
  ('win32com.server.dispatcher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE-2'),
  ('win32traceutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE-2'),
  ('win32com.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE-2'),
  ('win32com.server.exception',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE-2'),
  ('win32com.server.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE-2'),
  ('win32com.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE-2'),
  ('win32com.universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE-2'),
  ('pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE-2'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\__future__.py',
   'PYMODULE-2'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE-2'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\glob.py',
   'PYMODULE-2'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\configparser.py',
   'PYMODULE-2'),
  ('PIL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-2'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-2'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-2'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\__init__.py',
   'PYMODULE-2'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\log.py',
   'PYMODULE-2'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\threads.py',
   'PYMODULE-2'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-2'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\streams.py',
   'PYMODULE-2'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\queues.py',
   'PYMODULE-2'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\runners.py',
   'PYMODULE-2'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\__init__.py',
   'PYMODULE-2'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\trsock.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\tasks.py',
   'PYMODULE-2'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\locks.py',
   'PYMODULE-2'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\mixins.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\futures.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\events.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-2'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-2'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-2'),
  ('PIL.ImageGrab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE-2'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE-2'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE-2'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE-2'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE-2'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE-2'),
  ('PIL._util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-2'),
  ('PIL.features',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE-2'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-2'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE-2'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-2'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shlex.py',
   'PYMODULE-2'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-2'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-2'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-2'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-2'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-2'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-2'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-2'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-2'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-2'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fractions.py',
   'PYMODULE-2'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-2'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-2'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-2'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-2'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\colorsys.py',
   'PYMODULE-2'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-2'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-2'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-2'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-2'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE-2'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-2'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-2'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-2'),
  ('PIL._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-2'),
  ('Crypto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE-2'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE-2'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\stringprep.py',
   'PYMODULE-2'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_py_abc.py',
   'PYMODULE-2'),
  ('social', 'D:\\tool\\python\\bot4\\social.py', 'PYMODULE-2'),
  ('Crypto.Cipher.AES',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE-2'),
  ('Crypto.Random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE-2'),
  ('Crypto.Util._cpu_features',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE-2'),
  ('Crypto.Util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE-2'),
  ('Crypto.Util._raw_api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE-2'),
  ('Crypto.Util._file_system',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE-2'),
  ('Crypto.Util.py3compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE-2'),
  ('Crypto.Cipher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_kwp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kwp.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_kw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kw.py',
   'PYMODULE-2'),
  ('Crypto.Util.strxor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_ocb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE-2'),
  ('Crypto.Hash.BLAKE2s',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE-2'),
  ('Crypto.Hash',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE-2'),
  ('Crypto.Hash.HMAC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE-2'),
  ('Crypto.Hash.MD5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE-2'),
  ('Crypto.Hash.CMAC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA3_512',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE-2'),
  ('Crypto.Hash.keccak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA3_384',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA3_256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA3_224',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA512',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA384',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA224',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE-2'),
  ('Crypto.Hash.SHA1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE-2'),
  ('Crypto.Util.number',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_gcm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_siv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE-2'),
  ('Crypto.Protocol.KDF',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE-2'),
  ('Crypto.Protocol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._EKSBlowfish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_ccm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_eax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_openpgp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_ctr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_ofb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_cfb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_cbc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE-2'),
  ('Crypto.Cipher._mode_ecb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE-2'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE-2'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE-2'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE-2'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE-2'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookies.py',
   'PYMODULE-2'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE-2'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE-2'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE-2'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE-2'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE-2'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE-2'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE-2'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE-2'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE-2'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE-2'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE-2'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE-2'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE-2'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE-2'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE-2'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE-2'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE-2'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE-2'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE-2'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ipaddress.py',
   'PYMODULE-2'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE-2'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE-2'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE-2'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE-2'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE-2'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE-2'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE-2'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE-2'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE-2'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE-2'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE-2'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE-2'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE-2'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\mimetypes.py',
   'PYMODULE-2'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE-2'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE-2'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE-2'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE-2'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE-2'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE-2'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE-2'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\netrc.py',
   'PYMODULE-2'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE-2'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE-2'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE-2'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE-2'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE-2'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-2'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-2'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-2'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-2'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-2'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-2'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-2'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE-2'),
  ('wallet', 'D:\\tool\\python\\bot4\\wallet.py', 'PYMODULE-2'),
  ('win32con',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE-2'),
  ('telebot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\__init__.py',
   'PYMODULE-2'),
  ('telebot.ext.reloader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\ext\\reloader.py',
   'PYMODULE-2'),
  ('telebot.ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\ext\\__init__.py',
   'PYMODULE-2'),
  ('telebot.ext.sync',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\ext\\sync\\__init__.py',
   'PYMODULE-2'),
  ('telebot.ext.sync.webhooks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\ext\\sync\\webhooks.py',
   'PYMODULE-2'),
  ('telebot.custom_filters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\custom_filters.py',
   'PYMODULE-2'),
  ('telebot.states',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\states\\__init__.py',
   'PYMODULE-2'),
  ('telebot.handler_backends',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\handler_backends.py',
   'PYMODULE-2'),
  ('telebot.apihelper',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\apihelper.py',
   'PYMODULE-2'),
  ('telebot.storage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\storage\\__init__.py',
   'PYMODULE-2'),
  ('telebot.storage.base_storage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\storage\\base_storage.py',
   'PYMODULE-2'),
  ('telebot.storage.pickle_storage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\storage\\pickle_storage.py',
   'PYMODULE-2'),
  ('telebot.storage.redis_storage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\storage\\redis_storage.py',
   'PYMODULE-2'),
  ('telebot.storage.memory_storage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\storage\\memory_storage.py',
   'PYMODULE-2'),
  ('telebot.formatting',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\formatting.py',
   'PYMODULE-2'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\__init__.py',
   'PYMODULE-2'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\entities.py',
   'PYMODULE-2'),
  ('telebot.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\util.py',
   'PYMODULE-2'),
  ('telebot.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\types.py',
   'PYMODULE-2'),
  ('telebot.service_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\telebot\\service_utils.py',
   'PYMODULE-2'),
  ('cpuinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cpuinfo\\__init__.py',
   'PYMODULE-2'),
  ('cpuinfo.cpuinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cpuinfo\\cpuinfo.py',
   'PYMODULE-2'),
  ('psutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE-2'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE-2'),
  ('psutil._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE-2'),
  ('getmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\getmac\\__init__.py',
   'PYMODULE-2'),
  ('getmac.getmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\getmac\\getmac.py',
   'PYMODULE-2'),
  ('timeit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\timeit.py',
   'PYMODULE-2'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\uuid.py',
   'PYMODULE-2'),
  ('getmac.shutilwhich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\getmac\\shutilwhich.py',
   'PYMODULE-2'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\platform.py',
   'PYMODULE-2'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE-2'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getpass.py',
   'PYMODULE-2'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\nturl2path.py',
   'PYMODULE-2'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ftplib.py',
   'PYMODULE-2'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE-2'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE-2'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\subprocess.py',
   'PYMODULE-2'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\string.py',
   'PYMODULE-2'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\random.py',
   'PYMODULE-2'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\statistics.py',
   'PYMODULE-2'),
  ('pyautogui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyautogui\\__init__.py',
   'PYMODULE-2'),
  ('pyautogui._pyautogui_x11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyautogui\\_pyautogui_x11.py',
   'PYMODULE-2'),
  ('pyautogui._pyautogui_win',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyautogui\\_pyautogui_win.py',
   'PYMODULE-2'),
  ('pyautogui._pyautogui_osx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyautogui\\_pyautogui_osx.py',
   'PYMODULE-2'),
  ('pygetwindow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE-2'),
  ('pygetwindow._pygetwindow_win',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE-2'),
  ('pygetwindow._pygetwindow_macos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE-2'),
  ('pyrect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyrect\\__init__.py',
   'PYMODULE-2'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\doctest.py',
   'PYMODULE-2'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\__init__.py',
   'PYMODULE-2'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\async_case.py',
   'PYMODULE-2'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\signals.py',
   'PYMODULE-2'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\main.py',
   'PYMODULE-2'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\runner.py',
   'PYMODULE-2'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\loader.py',
   'PYMODULE-2'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\suite.py',
   'PYMODULE-2'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\case.py',
   'PYMODULE-2'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\_log.py',
   'PYMODULE-2'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\result.py',
   'PYMODULE-2'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\util.py',
   'PYMODULE-2'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pdb.py',
   'PYMODULE-2'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc.py',
   'PYMODULE-2'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\webbrowser.py',
   'PYMODULE-2'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\server.py',
   'PYMODULE-2'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socketserver.py',
   'PYMODULE-2'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE-2'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tty.py',
   'PYMODULE-2'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sysconfig.py',
   'PYMODULE-2'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_aix_support.py',
   'PYMODULE-2'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_bootsubprocess.py',
   'PYMODULE-2'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\code.py',
   'PYMODULE-2'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\codeop.py',
   'PYMODULE-2'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bdb.py',
   'PYMODULE-2'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cmd.py',
   'PYMODULE-2'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\difflib.py',
   'PYMODULE-2'),
  ('mouseinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mouseinfo\\__init__.py',
   'PYMODULE-2'),
  ('pyperclip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE-2'),
  ('pyscreeze',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyscreeze\\__init__.py',
   'PYMODULE-2'),
  ('pymsgbox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymsgbox\\__init__.py',
   'PYMODULE-2'),
  ('pymsgbox._native_win',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymsgbox\\_native_win.py',
   'PYMODULE-2'),
  ('pytweening',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytweening\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE-2'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE-2'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE-2'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE-2'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shutil.py',
   'PYMODULE-2'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tarfile.py',
   'PYMODULE-2'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tempfile.py',
   'PYMODULE-2')],
 [('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_curve25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Cryptodome\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Cryptodome\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Math\\_modexp.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_curve448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Cryptodome\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\_strxor.pyd',
   'BINARY'),
  ('Cryptodome\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('lz4\\_version.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4\\_version.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lz4\\block\\_block.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4\\block\\_block.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_avif.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_imagingft.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32crypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32crypt.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY')],
 [],
 [],
 [('pycountry\\locales\\be\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\be\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\bn\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\tr\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tr\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ar\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ar\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\nl\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nl\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\is\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\is\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\tr\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tr\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\kk\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kk\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\zu\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zu\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\uz\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\uz\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ka\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ka\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\bn_IN\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn_IN\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\iu\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\iu\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sv\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sv\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\cv\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cv\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ro\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ro\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\tzm\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tzm\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ml\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ml\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\mr\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mr\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\wa\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\wa\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ca\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ca\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\si\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\si\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fr\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fr\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\fa\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fa\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\id\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\id\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\rw\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\rw\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\pap\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pap\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pt\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\sc\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sc\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\bg\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bg\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\dv\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\dv\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\az\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\az\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ug\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ug\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\cs\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cs\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ne\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ne\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\xh\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\xh\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\as\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\as\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\bg\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bg\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\tt@iqtelif\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tt@iqtelif\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\databases\\iso3166-1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\databases\\iso3166-1.json',
   'DATA'),
  ('pycountry\\locales\\uk\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\uk\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\sl\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sl\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\km\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\km\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\lt\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lt\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ca\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ca\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\dz\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\dz\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\kab\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kab\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\gez\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gez\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ab\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ab\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\ru\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ru\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\nso\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nso\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ru\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ru\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\ms\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ms\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\sr\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\bg\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bg\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\km\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\km\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ug\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ug\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\id\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\id\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\be\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\be\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\ka\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ka\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\es\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\es\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\lt\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lt\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\mi\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mi\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ar\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ar\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\hu\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hu\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\cy\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cy\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ro\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ro\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\br\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\br\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\de\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\de\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\databases\\iso15924.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\databases\\iso15924.json',
   'DATA'),
  ('pycountry\\locales\\et\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\et\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ab\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ab\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sk\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sk\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\kv\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kv\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\hu\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hu\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\et\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\et\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ba\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ba\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fr\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fr\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\da\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\da\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ve\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ve\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\da\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\da\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\bar\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bar\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ga\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ga\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\pa\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pa\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\az\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\az\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\eo\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eo\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\nl\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nl\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\be\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\be\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\lv\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lv\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\kok\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kok\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\bn\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\is\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\is\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\sr\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\da\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\da\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\th\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\th\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\es\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\es\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\an\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\an\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fa\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fa\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\cs\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cs\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\sq\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sq\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\fr\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fr\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\crh\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\crh\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\sc\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sc\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sq\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sq\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\uk\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\uk\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\tr\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tr\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\br\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\br\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\pt\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\bn_IN\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn_IN\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\af\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\af\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ve\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ve\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\is\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\is\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\lv\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lv\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\kn\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kn\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\mn\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mn\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\hr\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hr\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\eo\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eo\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\da\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\da\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\or\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\or\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ay\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ay\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\it\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\it\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\tr\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tr\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\el\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\el\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\el\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\el\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\mk\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mk\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ps\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ps\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\lt\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lt\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\haw\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\haw\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fo\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fo\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\nso\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nso\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\tk\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tk\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pl\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pl\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\zh_HK\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_HK\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\ko\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ko\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\as\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\as\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ru\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ru\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\hr\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hr\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\pl\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pl\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\ta\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ta\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\sc\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sc\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\zh_HK\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_HK\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\cy\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cy\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\th\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\th\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\eo\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eo\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\id\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\id\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\lv\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lv\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\is\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\is\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\gl\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gl\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\cy\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cy\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ast\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ast\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ast\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ast\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ta\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ta\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\zh_Hant\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_Hant\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\ast\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ast\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\nn\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nn\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\de\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\de\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ee\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ee\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\nso\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nso\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ru\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ru\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ja\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ja\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\nn\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nn\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\tr\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tr\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\pa\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pa\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ta\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ta\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\bg\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bg\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\tzm\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tzm\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\hu\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hu\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ja\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ja\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\hi\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hi\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\bs\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bs\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\son\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\son\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\nl\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nl\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\kmr\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kmr\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\chr\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\chr\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pl\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pl\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\wo\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\wo\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\so\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\so\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\et\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\et\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ko\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ko\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\af\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\af\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\en\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\en\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\mt\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mt\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\tg\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tg\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fy\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fy\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pl\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pl\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\nn\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nn\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\mn\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mn\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry-24.6.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry-24.6.1.dist-info\\INSTALLER',
   'DATA'),
  ('pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\sr\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ast\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ast\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\xh\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\xh\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ko\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ko\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\sd\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sd\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fi\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fi\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\da\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\da\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\ga\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ga\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\sr\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\am\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\am\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\databases\\iso639-5.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\databases\\iso639-5.json',
   'DATA'),
  ('pycountry\\databases\\iso4217.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\databases\\iso4217.json',
   'DATA'),
  ('pycountry\\locales\\lt\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lt\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ro\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ro\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\pa_PK\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pa_PK\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\gl\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gl\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\pt\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ka\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ka\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\kk\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kk\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\mr\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mr\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\de\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\de\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ja\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ja\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\hu\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hu\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\id\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\id\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\wa\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\wa\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\kab\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kab\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\he\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\he\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\eo\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eo\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\et\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\et\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\fr\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fr\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ca\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ca\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\crh\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\crh\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\am\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\am\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\he\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\he\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\sr\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\zh_HK\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_HK\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\it\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\it\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\sq\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sq\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ur\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ur\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ne\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ne\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\mr\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mr\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\eu\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eu\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\bn_BD\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn_BD\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\nn\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nn\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\de\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\de\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\vi\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\vi\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ta\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ta\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\el\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\el\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\id\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\id\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sq\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sq\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\fr\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fr\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\mt\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mt\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\gez\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gez\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\af\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\af\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\cs\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cs\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\rw\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\rw\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\or\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\or\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ta\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ta\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\sq\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sq\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\fur\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fur\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ru\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ru\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\fo\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fo\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\yo\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\yo\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ky\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ky\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ja\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ja\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ve\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ve\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\be\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\be\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\nl\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nl\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\gu\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gu\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pl\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pl\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\tl\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tl\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\si\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\si\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ky\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ky\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\hr\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hr\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fil\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fil\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry-24.6.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry-24.6.1.dist-info\\METADATA',
   'DATA'),
  ('pycountry\\locales\\kab\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kab\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\fur\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fur\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\oc\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\oc\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\sv\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sv\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\hr\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hr\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry-24.6.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry-24.6.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('pycountry\\locales\\it\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\it\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\te\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\te\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\is\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\is\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fi\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fi\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\sw\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sw\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\tig\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tig\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\sw\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sw\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\cy\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cy\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\cy\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cy\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\wa\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\wa\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\eo\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eo\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\bs\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bs\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\bg\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bg\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\pi\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pi\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\hy\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hy\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\eu\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eu\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\tt\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tt\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ru\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ru\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\databases\\iso3166-3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\databases\\iso3166-3.json',
   'DATA'),
  ('pycountry\\locales\\sc\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sc\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\lv\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lv\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\fa\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fa\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\de\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\de\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ace\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ace\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry-24.6.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry-24.6.1.dist-info\\WHEEL',
   'DATA'),
  ('pycountry\\locales\\eu\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eu\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\lv\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lv\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\frp\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\frp\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\cs\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cs\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\gu\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gu\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\fr\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fr\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\bs\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bs\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\cs\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cs\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\zh_HK\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_HK\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\or\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\or\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\br\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\br\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\fur\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fur\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\eu\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eu\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\hu\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hu\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\kmr\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kmr\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\uk\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\uk\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\th\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\th\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ro\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ro\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\oc\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\oc\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\it\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\it\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\gl\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gl\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\my\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\my\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\cs\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cs\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\mt\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mt\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\mi\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mi\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\mhr\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mhr\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fa\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fa\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pt\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\mk\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mk\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\hr\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hr\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\cy\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cy\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\tig\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tig\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ht\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ht\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\bi\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bi\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ps\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ps\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\io\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\io\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sq\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sq\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\sk\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sk\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\zu\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zu\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\sc\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sc\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\nl\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nl\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\pa_PK\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pa_PK\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\gez\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gez\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\gu\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gu\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\pl\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pl\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ach\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ach\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\uk\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\uk\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\az\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\az\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\nl\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nl\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ko\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ko\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\br\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\br\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\tk\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tk\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\be\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\be\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\nso\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nso\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\lv\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lv\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ff\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ff\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ro\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ro\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\wo\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\wo\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\eo\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eo\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\te\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\te\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ast\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ast\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\pa_PK\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pa_PK\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ia\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ia\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry-24.6.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry-24.6.1.dist-info\\RECORD',
   'DATA'),
  ('pycountry\\locales\\lo\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lo\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sl\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sl\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\haw\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\haw\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\da\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\da\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\gl\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gl\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\is\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\is\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\zu\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zu\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\lt\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lt\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\mai\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mai\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\wal\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\wal\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ko\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ko\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\sc\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sc\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\nl\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nl\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\fr\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fr\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\mn\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mn\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\sl\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sl\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\kw\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kw\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sv\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sv\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\tt\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tt\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\bn\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\kab\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kab\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\uk\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\uk\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ta\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ta\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\ro_MD\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ro_MD\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ca\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ca\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\de\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\de\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\th\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\th\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ta\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ta\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\oc\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\oc\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\es\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\es\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\cs\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cs\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\hi\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hi\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\sk\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sk\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\sv\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sv\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\bn_BD\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn_BD\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\pt\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\cv\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cv\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ti\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ti\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ga\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ga\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\fil\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fil\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\es\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\es\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\cy\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\cy\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\eu\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eu\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\nv\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nv\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\el\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\el\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\sl\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sl\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\zh_Hans\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_Hans\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\rw\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\rw\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\oc\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\oc\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\si\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\si\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\so\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\so\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\so\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\so\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\byn\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\byn\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\rw\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\rw\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\be\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\be\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\fil\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fil\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ha\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ha\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\kmr\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kmr\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ga\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ga\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\pl\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pl\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\tr\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tr\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ia\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ia\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\es\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\es\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\be\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\be\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ru\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ru\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ko\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ko\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\it\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\it\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ti\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ti\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\sv\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sv\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\id\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\id\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\fur\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fur\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ml\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ml\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ti\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ti\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\et\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\et\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\byn\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\byn\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\so\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\so\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\sr\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ki\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ki\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\crh\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\crh\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\so\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\so\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\sk\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sk\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\ms\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ms\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\pa\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pa\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\sk\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sk\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\vi\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\vi\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\mi\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mi\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ka\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ka\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\py.typed',
   'DATA'),
  ('pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\hr\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hr\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\dz\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\dz\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\uk\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\uk\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\fi\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fi\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ja\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ja\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ml\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ml\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ka\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ka\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ast\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ast\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\fi\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fi\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\sv\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sv\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\ca\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ca\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\lt\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lt\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\id\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\id\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ps\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ps\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ast\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ast\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\lt\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\lt\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\tig\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tig\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ca\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ca\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\nah\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nah\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\et\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\et\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\databases\\iso639-3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\databases\\iso639-3.json',
   'DATA'),
  ('pycountry\\locales\\mk\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mk\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\el\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\el\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\he\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\he\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\kab\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kab\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ia\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ia\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\it\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\it\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\hr\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hr\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\sk\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sk\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\hi\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hi\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sl\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sl\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\tr\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tr\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\hy\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hy\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry-24.6.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry-24.6.1.dist-info\\REQUESTED',
   'DATA'),
  ('pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\br\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\br\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\km\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\km\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\oc\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\oc\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ga\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ga\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\jam\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\jam\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\databases\\iso3166-2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\databases\\iso3166-2.json',
   'DATA'),
  ('pycountry\\locales\\hu\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hu\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\ka\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ka\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ar\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ar\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\csb\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\csb\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\byn\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\byn\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\na\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\na\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\it\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\it\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\uk\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\uk\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\th\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\th\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\kn\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kn\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\sl\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sl\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\vi\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\vi\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\tt@iqtelif\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tt@iqtelif\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\vi\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\vi\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\tzm\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tzm\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\et\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\et\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_TW\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\bn_BD\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn_BD\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\so\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\so\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\COPYRIGHT.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\COPYRIGHT.txt',
   'DATA'),
  ('pycountry\\locales\\kn\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kn\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ve\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ve\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\sq\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sq\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\ckb\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ckb\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fa\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fa\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\zh_CN\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\wa\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\wa\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ar\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ar\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ar\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ar\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\el\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\el\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\vi\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\vi\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ce\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ce\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\eu\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eu\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\kab\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kab\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ja\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ja\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\hi\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hi\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr@latin\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\sc\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sc\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\gn\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gn\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\fi\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fi\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\gv\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gv\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\bs\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bs\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\gl\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\gl\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\tl\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tl\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\bn\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\km\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\km\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\kl\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\kl\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\oc\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\oc\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\oc\\LC_MESSAGES\\iso15924.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\oc\\LC_MESSAGES\\iso15924.mo',
   'DATA'),
  ('pycountry\\locales\\ms\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ms\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\da\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\da\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\nn\\LC_MESSAGES\\iso4217.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nn\\LC_MESSAGES\\iso4217.mo',
   'DATA'),
  ('pycountry\\locales\\hu\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\hu\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\de\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\de\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\wal\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\wal\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\az\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\az\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\pt_BR\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\is\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\is\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\crh\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\crh\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\xh\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\xh\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\nb_NO\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\mn\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\mn\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\es\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\es\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\ro\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ro\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ch\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ch\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sv\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sv\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\he\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\he\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\ka\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ka\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\ak\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ak\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\sr\\LC_MESSAGES\\iso639-5.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\sr\\LC_MESSAGES\\iso639-5.mo',
   'DATA'),
  ('pycountry\\locales\\el\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\el\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\th\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\th\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\fi\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\fi\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\tt@iqtelif\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tt@iqtelif\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\bn\\LC_MESSAGES\\iso3166-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\bn\\LC_MESSAGES\\iso3166-3.mo',
   'DATA'),
  ('pycountry\\locales\\tt\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\tt\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\am\\LC_MESSAGES\\iso3166-1.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\am\\LC_MESSAGES\\iso3166-1.mo',
   'DATA'),
  ('pycountry\\locales\\vi\\LC_MESSAGES\\iso3166-2.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\vi\\LC_MESSAGES\\iso3166-2.mo',
   'DATA'),
  ('pycountry\\locales\\eo\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\eo\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\ro\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\ro\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('pycountry\\locales\\he\\LC_MESSAGES\\iso639-3.mo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycountry\\locales\\he\\LC_MESSAGES\\iso639-3.mo',
   'DATA'),
  ('lz4-4.4.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\RECORD',
   'DATA'),
  ('lz4-4.4.4.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('lz4-4.4.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\top_level.txt',
   'DATA'),
  ('lz4-4.4.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\INSTALLER',
   'DATA'),
  ('lz4-4.4.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\METADATA',
   'DATA'),
  ('lz4-4.4.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\WHEEL',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('base_library.zip',
   'D:\\tool\\python\\bot4\\build\\TradingView\\base_library.zip',
   'DATA')],
 [('traceback',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\traceback.py',
   'PYMODULE'),
  ('functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\functools.py',
   'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\codecs.py',
   'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\genericpath.py',
   'PYMODULE'),
  ('re._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\operator.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\linecache.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\weakref.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\stat.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\copyreg.py',
   'PYMODULE'),
  ('enum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\enum.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\keyword.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\types.py',
   'PYMODULE'),
  ('io',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\io.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\posixpath.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\warnings.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\reprlib.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\locale.py',
   'PYMODULE'),
  ('abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\abc.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ntpath.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\heapq.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\os.py',
   'PYMODULE')])
