
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), getmac.getmac (conditional, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed), psutil (optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named org - imported by pickle (optional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named importlib_resources - imported by pycountry (optional)
missing module named sqlite3.connect - imported by sqlite3 (top-level), wallet (top-level), social (top-level)
missing module named cffi - imported by Crypto.Util._raw_api (optional), Cryptodome.Util._raw_api (optional)
missing module named StringIO - imported by Crypto.Util.py3compat (conditional), Cryptodome.Util.py3compat (conditional)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named dbus - imported by browser_cookie3 (conditional, optional)
missing module named 'jeepney.io' - imported by browser_cookie3 (conditional, optional)
missing module named jeepney - imported by browser_cookie3 (conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named 'numpy.typing' - imported by PIL._typing (conditional, optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named collections.Sequence - imported by collections (conditional), pyautogui (conditional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named 'watchdog.events' - imported by telebot.ext.reloader (top-level)
missing module named watchdog - imported by telebot (delayed, optional)
missing module named starlette - imported by telebot.ext.sync.webhooks (delayed)
missing module named uvicorn - imported by telebot.ext.sync.webhooks (optional)
missing module named 'fastapi.requests' - imported by telebot.ext.sync.webhooks (optional)
missing module named 'fastapi.responses' - imported by telebot.ext.sync.webhooks (optional)
missing module named fastapi - imported by telebot.ext.sync.webhooks (optional)
missing module named coloredlogs - imported by telebot (delayed, conditional, optional)
missing module named redis - imported by telebot.storage.redis_storage (optional), telebot.handler_backends (optional)
missing module named 'requests.packages.urllib3' - imported by telebot.apihelper (optional)
missing module named ujson - imported by telebot.types (optional), telebot.util (optional), telebot.apihelper (optional)
missing module named multiprocessing.Queue - imported by multiprocessing (delayed), cpuinfo.cpuinfo (delayed)
missing module named multiprocessing.Process - imported by multiprocessing (delayed), cpuinfo.cpuinfo (delayed)
missing module named _winreg - imported by platform (delayed, optional), cpuinfo.cpuinfo (delayed, optional)
missing module named fcntl - imported by subprocess (optional), getmac.getmac (delayed, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional)
missing module named 'Xlib.XK' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named 'Xlib.ext' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named Xlib - imported by mouseinfo (conditional), pyautogui._pyautogui_x11 (top-level)
missing module named 'Xlib.display' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional), pyautogui._pyautogui_osx (top-level)
missing module named Quartz - imported by pygetwindow._pygetwindow_macos (top-level), pyautogui._pyautogui_osx (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional)
excluded module named tkinter - imported by pymsgbox (conditional, optional), mouseinfo (conditional, optional)
missing module named Tkinter - imported by pymsgbox (conditional, optional), mouseinfo (conditional, optional)
missing module named 'rubicon.objc' - imported by mouseinfo (conditional)
missing module named rubicon - imported by mouseinfo (conditional)
missing module named Foundation - imported by pyperclip (delayed, conditional, optional)
missing module named PyQt5 - imported by pyperclip (delayed, conditional, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional)
excluded module named numpy - imported by pyscreeze (optional)
missing module named cv2 - imported by pyscreeze (optional)
missing module named multiprocessing.freeze_support - imported by multiprocessing (top-level), D:\tool\python\bot4\main.py (top-level)
