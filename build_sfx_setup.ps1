# TradingView Self-Extracting Setup Builder
# Tạo file setup tự giải nén và cài đặt
# Không cần NSIS, chỉ cần 7-Zip

param(
    [string]$AppName = "TradingView",
    [string]$AppVersion = "1.0.0",
    [string]$SevenZipPath = "C:\Program Files\7-Zip\7z.exe",
    [switch]$Verbose = $false
)

function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Step { param($Message) Write-Host $Message -ForegroundColor Blue }

Write-Host "========================================" -ForegroundColor Magenta
Write-Host "TradingView SFX Setup Builder" -ForegroundColor Magenta
Write-Host "Tạo file setup tự cài đặt" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Magenta

$ProjectDir = Get-Location
$DistDir = Join-Path $ProjectDir "dist"
$SfxDir = Join-Path $ProjectDir "sfx_temp"
$OutputDir = Join-Path $ProjectDir "setup_output"

# Create directories
@($SfxDir, $OutputDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

Write-Step "`n🔨 Step 1: Building main executable..."

# Build main executable first
if (Test-Path "build_advanced.ps1") {
    & .\build_advanced.ps1 -Verbose:$Verbose
} else {
    Write-Error "❌ build_advanced.ps1 not found!"
    exit 1
}

$ExePath = Join-Path $DistDir "$AppName.exe"
if (-not (Test-Path $ExePath)) {
    Write-Error "❌ Main executable not found!"
    exit 1
}

Write-Success "✓ Main executable ready"

Write-Step "`n📦 Step 2: Preparing installation package..."

# Clean SFX directory
if (Test-Path $SfxDir) { Remove-Item $SfxDir -Recurse -Force }
New-Item -ItemType Directory -Path $SfxDir | Out-Null

# Copy main executable
Copy-Item $ExePath $SfxDir -Force
Write-Info "  Copied: $AppName.exe"

# Copy additional files
$AdditionalFiles = @("logo.ico")
foreach ($file in $AdditionalFiles) {
    if (Test-Path $file) {
        Copy-Item $file $SfxDir -Force
        Write-Info "  Copied: $file"
    }
}

Write-Step "`n📝 Step 3: Creating installation script..."

# Create installation script
$InstallScript = @"
@echo off
title TradingView Setup
color 0A
echo.
echo ========================================
echo    TradingView Installation
echo ========================================
echo.

REM Get installation directory
set "INSTALL_DIR=%LOCALAPPDATA%\TradingView"

echo Installing TradingView...
echo Target directory: %INSTALL_DIR%
echo.

REM Create installation directory
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo ✓ Created installation directory
) else (
    echo ✓ Installation directory exists
)

REM Copy files
echo Copying application files...
copy /Y "$AppName.exe" "%INSTALL_DIR%\" >nul 2>&1
if errorlevel 1 (
    echo ❌ Failed to copy main executable
    pause
    exit /b 1
)
echo ✓ Main executable copied

if exist "logo.ico" (
    copy /Y "logo.ico" "%INSTALL_DIR%\" >nul 2>&1
    echo ✓ Icon file copied
)

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -WindowStyle Hidden -Command "& {`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%USERPROFILE%\Desktop\TradingView.lnk'); `$Shortcut.TargetPath = '%INSTALL_DIR%\$AppName.exe'; if (Test-Path '%INSTALL_DIR%\logo.ico') {`$Shortcut.IconLocation = '%INSTALL_DIR%\logo.ico'}; `$Shortcut.Save()}" 2>nul
if errorlevel 1 (
    echo ⚠ Desktop shortcut creation failed
) else (
    echo ✓ Desktop shortcut created
)

REM Create start menu shortcut
echo Creating start menu shortcut...
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\TradingView" (
    mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\TradingView"
)
powershell -WindowStyle Hidden -Command "& {`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\TradingView\TradingView.lnk'); `$Shortcut.TargetPath = '%INSTALL_DIR%\$AppName.exe'; if (Test-Path '%INSTALL_DIR%\logo.ico') {`$Shortcut.IconLocation = '%INSTALL_DIR%\logo.ico'}; `$Shortcut.Save()}" 2>nul
echo ✓ Start menu shortcut created

REM Add to Windows startup (optional)
echo Adding to Windows startup...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "TradingView" /t REG_SZ /d "%INSTALL_DIR%\$AppName.exe" /f >nul 2>&1
if errorlevel 1 (
    echo ⚠ Startup entry creation failed
) else (
    echo ✓ Added to Windows startup
)

REM Create uninstaller
echo Creating uninstaller...
(
echo @echo off
echo title TradingView Uninstaller
echo echo Removing TradingView...
echo.
echo REM Remove from startup
echo reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "TradingView" /f ^>nul 2^>^&1
echo.
echo REM Remove shortcuts
echo del "%USERPROFILE%\Desktop\TradingView.lnk" ^>nul 2^>^&1
echo rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\TradingView" ^>nul 2^>^&1
echo.
echo REM Remove program files
echo cd /d "%LOCALAPPDATA%"
echo if exist "TradingView" ^(
echo     rmdir /s /q "TradingView"
echo     echo TradingView removed successfully.
echo ^) else ^(
echo     echo TradingView not found.
echo ^)
echo.
echo echo Uninstallation completed.
echo pause
echo del "%%~f0"
) > "%INSTALL_DIR%\Uninstall.bat"
echo ✓ Uninstaller created

REM Register in Add/Remove Programs
echo Registering in Add/Remove Programs...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\TradingView" /v "DisplayName" /t REG_SZ /d "TradingView" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\TradingView" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\Uninstall.bat" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\TradingView" /v "DisplayVersion" /t REG_SZ /d "$AppVersion" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\TradingView" /v "Publisher" /t REG_SZ /d "TradingView Inc" /f >nul 2>&1
if exist "%INSTALL_DIR%\logo.ico" (
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\TradingView" /v "DisplayIcon" /t REG_SZ /d "%INSTALL_DIR%\logo.ico" /f >nul 2>&1
)
echo ✓ Registered in Add/Remove Programs

echo.
echo ========================================
echo    Installation Completed Successfully!
echo ========================================
echo.
echo TradingView has been installed to:
echo %INSTALL_DIR%
echo.
echo ✓ Desktop shortcut created
echo ✓ Start menu shortcut created  
echo ✓ Added to Windows startup
echo ✓ Registered in Add/Remove Programs
echo.
echo You can now close this window.
echo TradingView will start automatically with Windows.
echo.

REM Ask if user wants to start now
set /p "START_NOW=Do you want to start TradingView now? (Y/N): "
if /i "%START_NOW%"=="Y" (
    echo Starting TradingView...
    start "" "%INSTALL_DIR%\$AppName.exe"
)

REM Clean up installation files
echo Cleaning up...
cd /d "%TEMP%"
timeout /t 2 /nobreak >nul
rmdir /s /q "%~dp0" 2>nul

echo.
echo Press any key to exit...
pause >nul
"@

$InstallScriptPath = Join-Path $SfxDir "install.bat"
$InstallScript | Out-File -FilePath $InstallScriptPath -Encoding ASCII

Write-Success "✓ Installation script created"

Write-Step "`n🗜️ Step 4: Creating SFX configuration..."

# Create SFX configuration
$SfxConfig = @"
;!@Install@!UTF-8!
Title="TradingView Setup v$AppVersion"
BeginPrompt="Do you want to install TradingView?"
RunProgram="install.bat"
;!@InstallEnd@!
"@

$SfxConfigPath = Join-Path $SfxDir "config.txt"
$SfxConfig | Out-File -FilePath $SfxConfigPath -Encoding UTF8

Write-Step "`n📦 Step 5: Building self-extracting setup..."

# Check for 7-Zip
$SevenZipPaths = @(
    $SevenZipPath,
    "C:\Program Files\7-Zip\7z.exe",
    "C:\Program Files (x86)\7-Zip\7z.exe",
    "${env:ProgramFiles}\7-Zip\7z.exe",
    "${env:ProgramFiles(x86)}\7-Zip\7z.exe"
) | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $SevenZipPaths) {
    Write-Warning "⚠ 7-Zip not found. Creating simple batch installer..."
    
    # Create simple batch installer
    $SimpleBatchPath = Join-Path $OutputDir "${AppName}_Setup_v${AppVersion}.bat"
    Copy-Item $InstallScriptPath $SimpleBatchPath -Force
    
    Write-Info "📁 Simple installer: $SimpleBatchPath"
    Write-Warning "Note: Copy $AppName.exe and logo.ico to the same folder as the installer"
    
} else {
    Write-Info "Found 7-Zip: $SevenZipPaths"
    
    # Create temporary archive
    $TempArchive = Join-Path $SfxDir "temp.7z"
    Push-Location $SfxDir
    
    try {
        # Create 7z archive
        if ($Verbose) {
            & $SevenZipPaths a -t7z -mx9 $TempArchive "*"
        } else {
            & $SevenZipPaths a -t7z -mx9 $TempArchive "*" 2>$null | Out-Null
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✓ Archive created"
            
            # Find 7z SFX module
            $SfxModule = Join-Path (Split-Path $SevenZipPaths) "7zS.sfx"
            if (-not (Test-Path $SfxModule)) {
                $SfxModule = Join-Path (Split-Path $SevenZipPaths) "7zSD.sfx"
            }
            
            if (Test-Path $SfxModule) {
                # Create self-extracting executable
                $SetupPath = Join-Path $OutputDir "${AppName}_Setup_v${AppVersion}.exe"
                
                # Combine SFX module + config + archive
                $ConfigBytes = [System.IO.File]::ReadAllBytes($SfxConfigPath)
                $ArchiveBytes = [System.IO.File]::ReadAllBytes($TempArchive)
                $SfxBytes = [System.IO.File]::ReadAllBytes($SfxModule)
                
                $OutputBytes = $SfxBytes + $ConfigBytes + $ArchiveBytes
                [System.IO.File]::WriteAllBytes($SetupPath, $OutputBytes)
                
                if (Test-Path $SetupPath) {
                    $SetupSize = [math]::Round((Get-Item $SetupPath).Length / 1MB, 2)
                    Write-Success "✓ Self-extracting setup created!"
                    Write-Info "📁 Setup file: $SetupPath"
                    Write-Info "📊 Size: $SetupSize MB"
                } else {
                    Write-Error "❌ Failed to create setup file"
                }
            } else {
                Write-Error "❌ 7-Zip SFX module not found"
            }
        } else {
            Write-Error "❌ Failed to create archive"
        }
    } finally {
        Pop-Location
    }
}

Write-Step "`n🧹 Cleaning up..."
if (Test-Path $SfxDir) {
    Remove-Item $SfxDir -Recurse -Force
}

Write-Success "`n🎉 Setup creation completed!"

Write-Step "`n📁 Output files:"
Get-ChildItem $OutputDir | ForEach-Object {
    $size = if ($_.PSIsContainer) { "DIR" } else { "$([math]::Round($_.Length / 1MB, 2)) MB" }
    Write-Host "  📄 $($_.Name) ($size)" -ForegroundColor White
}

Write-Host "`n🚀 Usage Instructions:" -ForegroundColor Yellow
Write-Host "• Double-click setup file to install" -ForegroundColor White
Write-Host "• Setup will auto-extract and install" -ForegroundColor White
Write-Host "• Creates desktop and start menu shortcuts" -ForegroundColor White
Write-Host "• Adds to Windows startup automatically" -ForegroundColor White
Write-Host "• Registers in Add/Remove Programs" -ForegroundColor White

Write-Host "`n⚠️  Features:" -ForegroundColor Green
Write-Host "• ✓ One-click installation" -ForegroundColor White
Write-Host "• ✓ Auto-start with Windows" -ForegroundColor White
Write-Host "• ✓ Desktop shortcut" -ForegroundColor White
Write-Host "• ✓ Start menu entry" -ForegroundColor White
Write-Host "• ✓ Uninstaller included" -ForegroundColor White
Write-Host "• ✓ Add/Remove Programs support" -ForegroundColor White

Read-Host "`nPress Enter to exit"
