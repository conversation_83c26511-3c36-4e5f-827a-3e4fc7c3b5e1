# Simple TradingView Setup Builder
# Tạo file setup đơn giản không cần tool bên ngoài
# Chỉ cần PowerShell có sẵn trong Windows

param(
    [string]$AppName = "TradingView",
    [string]$AppVersion = "1.0.0"
)

function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Step { param($Message) Write-Host $Message -ForegroundColor Blue }

Write-Host "========================================" -ForegroundColor Magenta
Write-Host "Simple TradingView Setup Builder" -ForegroundColor Magenta
Write-Host "Không cần tool bên ngoài" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Magenta

$ProjectDir = Get-Location
$DistDir = Join-Path $ProjectDir "dist"
$OutputDir = Join-Path $ProjectDir "setup_output"

if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
}

Write-Step "`n🔨 Step 1: Building main executable..."

# Build main executable
if (Test-Path "build_advanced.ps1") {
    & .\build_advanced.ps1
} else {
    Write-Error "❌ build_advanced.ps1 not found!"
    exit 1
}

$ExePath = Join-Path $DistDir "$AppName.exe"
if (-not (Test-Path $ExePath)) {
    Write-Error "❌ Main executable not found!"
    exit 1
}

Write-Success "✓ Main executable ready"

Write-Step "`n📝 Step 2: Creating embedded installer..."

# Read executable as base64
Write-Info "Encoding executable to base64..."
$ExeBytes = [System.IO.File]::ReadAllBytes($ExePath)
$ExeBase64 = [System.Convert]::ToBase64String($ExeBytes)

# Read icon if exists
$IconBase64 = ""
if (Test-Path "logo.ico") {
    $IconBytes = [System.IO.File]::ReadAllBytes("logo.ico")
    $IconBase64 = [System.Convert]::ToBase64String($IconBytes)
}

Write-Step "`n🎨 Step 3: Creating self-contained installer..."

# Create self-contained PowerShell installer
$InstallerScript = @"
# TradingView Self-Contained Installer
# Generated automatically - contains embedded executable

param([switch]`$Silent)

# Embedded data (base64 encoded)
`$ExeData = @"
$ExeBase64
"@

`$IconData = @"
$IconBase64
"@

function Write-Status { param(`$Message) if (-not `$Silent) { Write-Host `$Message -ForegroundColor Green } }
function Write-Info { param(`$Message) if (-not `$Silent) { Write-Host `$Message -ForegroundColor Cyan } }

if (-not `$Silent) {
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host "TradingView Installer v$AppVersion" -ForegroundColor Magenta
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host ""
}

try {
    # Installation directory
    `$InstallDir = "`$env:LOCALAPPDATA\TradingView"
    
    Write-Status "Installing TradingView..."
    Write-Info "Target: `$InstallDir"
    
    # Create installation directory
    if (-not (Test-Path `$InstallDir)) {
        New-Item -ItemType Directory -Path `$InstallDir -Force | Out-Null
        Write-Status "✓ Created installation directory"
    }
    
    # Decode and save executable
    Write-Status "Extracting application files..."
    `$ExeBytes = [System.Convert]::FromBase64String(`$ExeData)
    `$ExePath = Join-Path `$InstallDir "$AppName.exe"
    [System.IO.File]::WriteAllBytes(`$ExePath, `$ExeBytes)
    Write-Status "✓ Main executable extracted"
    
    # Decode and save icon if available
    if (`$IconData -ne "") {
        `$IconBytes = [System.Convert]::FromBase64String(`$IconData)
        `$IconPath = Join-Path `$InstallDir "logo.ico"
        [System.IO.File]::WriteAllBytes(`$IconPath, `$IconBytes)
        Write-Status "✓ Icon file extracted"
    }
    
    # Create desktop shortcut
    Write-Status "Creating shortcuts..."
    `$WshShell = New-Object -ComObject WScript.Shell
    `$DesktopShortcut = `$WshShell.CreateShortcut("`$env:USERPROFILE\Desktop\TradingView.lnk")
    `$DesktopShortcut.TargetPath = `$ExePath
    if (Test-Path (Join-Path `$InstallDir "logo.ico")) {
        `$DesktopShortcut.IconLocation = Join-Path `$InstallDir "logo.ico"
    }
    `$DesktopShortcut.Save()
    Write-Status "✓ Desktop shortcut created"
    
    # Create start menu shortcut
    `$StartMenuDir = "`$env:APPDATA\Microsoft\Windows\Start Menu\Programs\TradingView"
    if (-not (Test-Path `$StartMenuDir)) {
        New-Item -ItemType Directory -Path `$StartMenuDir -Force | Out-Null
    }
    `$StartMenuShortcut = `$WshShell.CreateShortcut("`$StartMenuDir\TradingView.lnk")
    `$StartMenuShortcut.TargetPath = `$ExePath
    if (Test-Path (Join-Path `$InstallDir "logo.ico")) {
        `$StartMenuShortcut.IconLocation = Join-Path `$InstallDir "logo.ico"
    }
    `$StartMenuShortcut.Save()
    Write-Status "✓ Start menu shortcut created"
    
    # Add to Windows startup
    Write-Status "Adding to Windows startup..."
    `$RegPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
    Set-ItemProperty -Path `$RegPath -Name "TradingView" -Value `$ExePath -Force
    Write-Status "✓ Added to Windows startup"
    
    # Create uninstaller
    Write-Status "Creating uninstaller..."
    `$UninstallScript = @'
# TradingView Uninstaller
Write-Host "Removing TradingView..." -ForegroundColor Yellow

# Remove from startup
try {
    Remove-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run" -Name "TradingView" -ErrorAction SilentlyContinue
    Write-Host "✓ Removed from startup" -ForegroundColor Green
} catch {}

# Remove shortcuts
try {
    Remove-Item "`$env:USERPROFILE\Desktop\TradingView.lnk" -ErrorAction SilentlyContinue
    Remove-Item "`$env:APPDATA\Microsoft\Windows\Start Menu\Programs\TradingView" -Recurse -ErrorAction SilentlyContinue
    Write-Host "✓ Shortcuts removed" -ForegroundColor Green
} catch {}

# Remove program files
try {
    Remove-Item "`$env:LOCALAPPDATA\TradingView" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Program files removed" -ForegroundColor Green
} catch {}

# Remove registry entries
try {
    Remove-Item "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\TradingView" -Recurse -ErrorAction SilentlyContinue
    Write-Host "✓ Registry entries removed" -ForegroundColor Green
} catch {}

Write-Host "TradingView has been completely removed." -ForegroundColor Green
Read-Host "Press Enter to exit"
'@
    
    `$UninstallPath = Join-Path `$InstallDir "Uninstall.ps1"
    `$UninstallScript | Out-File -FilePath `$UninstallPath -Encoding UTF8
    Write-Status "✓ Uninstaller created"
    
    # Register in Add/Remove Programs
    Write-Status "Registering in Add/Remove Programs..."
    `$UninstallRegPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\TradingView"
    if (-not (Test-Path `$UninstallRegPath)) {
        New-Item -Path `$UninstallRegPath -Force | Out-Null
    }
    Set-ItemProperty -Path `$UninstallRegPath -Name "DisplayName" -Value "TradingView"
    Set-ItemProperty -Path `$UninstallRegPath -Name "DisplayVersion" -Value "$AppVersion"
    Set-ItemProperty -Path `$UninstallRegPath -Name "Publisher" -Value "TradingView Inc"
    Set-ItemProperty -Path `$UninstallRegPath -Name "UninstallString" -Value "powershell.exe -ExecutionPolicy Bypass -File `"`$UninstallPath`""
    if (Test-Path (Join-Path `$InstallDir "logo.ico")) {
        Set-ItemProperty -Path `$UninstallRegPath -Name "DisplayIcon" -Value (Join-Path `$InstallDir "logo.ico")
    }
    Write-Status "✓ Registered in Add/Remove Programs"
    
    if (-not `$Silent) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Installation Completed Successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "TradingView has been installed to:" -ForegroundColor Cyan
        Write-Host `$InstallDir -ForegroundColor White
        Write-Host ""
        Write-Host "✓ Desktop shortcut created" -ForegroundColor Green
        Write-Host "✓ Start menu shortcut created" -ForegroundColor Green
        Write-Host "✓ Added to Windows startup" -ForegroundColor Green
        Write-Host "✓ Registered in Add/Remove Programs" -ForegroundColor Green
        Write-Host ""
        
        `$StartNow = Read-Host "Do you want to start TradingView now? (Y/N)"
        if (`$StartNow -eq "Y" -or `$StartNow -eq "y") {
            Start-Process `$ExePath
            Write-Host "TradingView started!" -ForegroundColor Green
        }
    }
    
} catch {
    Write-Host "❌ Installation failed: `$(`$_.Exception.Message)" -ForegroundColor Red
    if (-not `$Silent) {
        Read-Host "Press Enter to exit"
    }
    exit 1
}

if (-not `$Silent) {
    Write-Host ""
    Write-Host "Installation completed. You can close this window." -ForegroundColor Green
    Read-Host "Press Enter to exit"
}
"@

# Save installer script
$InstallerPath = Join-Path $OutputDir "${AppName}_Setup_v${AppVersion}.ps1"
$InstallerScript | Out-File -FilePath $InstallerPath -Encoding UTF8

Write-Success "✓ PowerShell installer created"

Write-Step "`n🔧 Step 4: Creating batch launcher..."

# Create batch file to run PowerShell installer
$BatchLauncher = @"
@echo off
title TradingView Setup
echo Starting TradingView installer...
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if errorlevel 1 (
    echo ❌ PowerShell is not available on this system.
    echo Please install PowerShell or use Windows 10/11.
    pause
    exit /b 1
)

REM Run PowerShell installer
powershell -ExecutionPolicy Bypass -File "%~dp0${AppName}_Setup_v${AppVersion}.ps1"

if errorlevel 1 (
    echo.
    echo ❌ Installation failed.
    pause
) else (
    echo.
    echo ✓ Installation completed successfully.
)
"@

$BatchPath = Join-Path $OutputDir "${AppName}_Setup_v${AppVersion}.bat"
$BatchLauncher | Out-File -FilePath $BatchPath -Encoding ASCII

Write-Success "✓ Batch launcher created"

Write-Step "`n📋 Step 5: Creating silent installer..."

# Create silent installer script
$SilentInstaller = @"
# Silent TradingView Installer
# Run with: powershell -ExecutionPolicy Bypass -File silent_install.ps1

Write-Host "Installing TradingView silently..." -ForegroundColor Green

# Run main installer in silent mode
& "`$PSScriptRoot\${AppName}_Setup_v${AppVersion}.ps1" -Silent

if (`$LASTEXITCODE -eq 0) {
    Write-Host "✓ TradingView installed successfully" -ForegroundColor Green
    
    # Start the application
    `$ExePath = "`$env:LOCALAPPDATA\TradingView\$AppName.exe"
    if (Test-Path `$ExePath) {
        Start-Process `$ExePath
        Write-Host "✓ TradingView started" -ForegroundColor Green
    }
} else {
    Write-Host "❌ Installation failed" -ForegroundColor Red
    exit 1
}
"@

$SilentPath = Join-Path $OutputDir "silent_install.ps1"
$SilentInstaller | Out-File -FilePath $SilentPath -Encoding UTF8

Write-Success "✓ Silent installer created"

Write-Success "`n🎉 Setup creation completed!"

Write-Step "`n📁 Output files:"
Get-ChildItem $OutputDir | ForEach-Object {
    $size = if ($_.PSIsContainer) { "DIR" } else { "$([math]::Round($_.Length / 1MB, 2)) MB" }
    Write-Host "  📄 $($_.Name) ($size)" -ForegroundColor White
}

Write-Host "`n🚀 Usage Instructions:" -ForegroundColor Yellow
Write-Host "• Double-click .bat file for easy installation" -ForegroundColor White
Write-Host "• Or run .ps1 file directly in PowerShell" -ForegroundColor White
Write-Host "• Use silent_install.ps1 for automated deployment" -ForegroundColor White

Write-Host "`n⚠️  Features:" -ForegroundColor Green
Write-Host "• ✓ Self-contained (no external dependencies)" -ForegroundColor White
Write-Host "• ✓ Embedded executable (single file)" -ForegroundColor White
Write-Host "• ✓ Auto-start with Windows" -ForegroundColor White
Write-Host "• ✓ Desktop and Start menu shortcuts" -ForegroundColor White
Write-Host "• ✓ Add/Remove Programs support" -ForegroundColor White
Write-Host "• ✓ Complete uninstaller" -ForegroundColor White
Write-Host "• ✓ Silent installation option" -ForegroundColor White

Write-Host "`n📋 Installation Details:" -ForegroundColor Cyan
Write-Host "• Install Location: %LOCALAPPDATA%\TradingView" -ForegroundColor White
Write-Host "• Shortcuts: Desktop + Start Menu" -ForegroundColor White
Write-Host "• Startup: Automatic with Windows" -ForegroundColor White
Write-Host "• Uninstall: Control Panel > Programs" -ForegroundColor White

Read-Host "`nPress Enter to exit"
