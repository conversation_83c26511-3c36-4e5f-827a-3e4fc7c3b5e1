# TradingView Installer Builder
# Tạo file setup cài đặt tự động
# Educational purposes only

param(
    [string]$AppName = "TradingView",
    [string]$AppVersion = "1.0.0",
    [string]$Publisher = "TradingView Inc",
    [string]$InstallDir = "TradingView",
    [switch]$CreateMSI = $false,
    [switch]$CreateNSIS = $true,
    [switch]$Verbose = $false
)

# Color functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Step { param($Message) Write-Host $Message -ForegroundColor Blue }

Write-Host "========================================" -ForegroundColor Magenta
Write-Host "TradingView Installer Builder v1.0" -ForegroundColor Magenta
Write-Host "Tạo file setup cài đặt tự động" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Magenta

# Configuration
$ProjectDir = Get-Location
$VenvDir = Join-Path $ProjectDir "venv"
$DistDir = Join-Path $ProjectDir "dist"
$InstallerDir = Join-Path $ProjectDir "installer"
$OutputDir = Join-Path $ProjectDir "setup_output"

# Create directories
@($InstallerDir, $OutputDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

Write-Step "`n🔨 Step 1: Building main executable..."

# First build the main executable
if (-not (Test-Path "build_advanced.ps1")) {
    Write-Error "❌ build_advanced.ps1 not found! Please run this script in the project directory."
    exit 1
}

# Run the advanced build script
& .\build_advanced.ps1 -Verbose:$Verbose

# Check if executable was created
$ExePath = Join-Path $DistDir "$AppName.exe"
if (-not (Test-Path $ExePath)) {
    Write-Error "❌ Main executable not found! Build failed."
    exit 1
}

Write-Success "✓ Main executable built successfully"

Write-Step "`n📦 Step 2: Creating installer package..."

# Copy executable to installer directory
$InstallerExePath = Join-Path $InstallerDir "$AppName.exe"
Copy-Item $ExePath $InstallerExePath -Force

# Copy additional files
$AdditionalFiles = @("logo.ico", "README.md")
foreach ($file in $AdditionalFiles) {
    if (Test-Path $file) {
        Copy-Item $file $InstallerDir -Force
        Write-Info "  Copied: $file"
    }
}

# Create uninstaller script
$UninstallerScript = @"
@echo off
title TradingView Uninstaller
echo Removing TradingView...

REM Remove from startup
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "TradingView" /f >nul 2>&1

REM Remove desktop shortcut
del "%USERPROFILE%\Desktop\TradingView.lnk" >nul 2>&1

REM Remove start menu shortcut
del "%APPDATA%\Microsoft\Windows\Start Menu\Programs\TradingView.lnk" >nul 2>&1

REM Remove program files
cd /d "%LOCALAPPDATA%"
if exist "TradingView" (
    rmdir /s /q "TradingView"
    echo TradingView removed successfully.
) else (
    echo TradingView not found.
)

REM Remove registry entries
reg delete "HKCU\Software\TradingView" /f >nul 2>&1

echo Uninstallation completed.
pause
del "%~f0"
"@

$UninstallerPath = Join-Path $InstallerDir "uninstall.bat"
$UninstallerScript | Out-File -FilePath $UninstallerPath -Encoding ASCII

Write-Step "`n🎨 Step 3: Creating NSIS installer script..."

# Create NSIS installer script
$NSISScript = @"
; TradingView Installer Script
; Generated automatically

!define APP_NAME "$AppName"
!define APP_VERSION "$AppVersion"
!define PUBLISHER "$Publisher"
!define INSTALL_DIR "$InstallDir"

; Include Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"

; General settings
Name "`${APP_NAME}"
OutFile "$OutputDir\`${APP_NAME}_Setup_v`${APP_VERSION}.exe"
InstallDir "`$LOCALAPPDATA\`${INSTALL_DIR}"
InstallDirRegKey HKCU "Software\`${APP_NAME}" "InstallDir"
RequestExecutionLevel user

; Interface settings
!define MUI_ABORTWARNING
!define MUI_ICON "logo.ico"
!define MUI_UNICON "logo.ico"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "license.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "English"

; Version information
VIProductVersion "$AppVersion.0"
VIAddVersionKey "ProductName" "`${APP_NAME}"
VIAddVersionKey "CompanyName" "`${PUBLISHER}"
VIAddVersionKey "FileVersion" "`${APP_VERSION}"
VIAddVersionKey "ProductVersion" "`${APP_VERSION}"
VIAddVersionKey "FileDescription" "`${APP_NAME} Installer"

; Installer sections
Section "MainSection" SEC01
    SetOutPath "`$INSTDIR"
    
    ; Copy files
    File "$AppName.exe"
    File /nonfatal "logo.ico"
    File /nonfatal "README.md"
    File "uninstall.bat"
    
    ; Create shortcuts
    CreateDirectory "`$SMPROGRAMS\`${APP_NAME}"
    CreateShortCut "`$SMPROGRAMS\`${APP_NAME}\`${APP_NAME}.lnk" "`$INSTDIR\`${APP_NAME}.exe" "" "`$INSTDIR\logo.ico"
    CreateShortCut "`$SMPROGRAMS\`${APP_NAME}\Uninstall.lnk" "`$INSTDIR\uninstall.bat"
    CreateShortCut "`$DESKTOP\`${APP_NAME}.lnk" "`$INSTDIR\`${APP_NAME}.exe" "" "`$INSTDIR\logo.ico"
    
    ; Registry entries
    WriteRegStr HKCU "Software\`${APP_NAME}" "InstallDir" "`$INSTDIR"
    WriteRegStr HKCU "Software\`${APP_NAME}" "Version" "`${APP_VERSION}"
    
    ; Add to startup (optional)
    WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "`${APP_NAME}" "`$INSTDIR\`${APP_NAME}.exe"
    
    ; Uninstaller
    WriteUninstaller "`$INSTDIR\Uninstall.exe"
    
    ; Add/Remove Programs entry
    WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}" "DisplayName" "`${APP_NAME}"
    WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}" "UninstallString" "`$INSTDIR\Uninstall.exe"
    WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}" "DisplayIcon" "`$INSTDIR\logo.ico"
    WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}" "Publisher" "`${PUBLISHER}"
    WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}" "DisplayVersion" "`${APP_VERSION}"
    
    `${GetSize} "`$INSTDIR" "/S=0K" `$0 `$1 `$2
    IntFmt `$0 "0x%08X" `$0
    WriteRegDWORD HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}" "EstimatedSize" "`$0"
SectionEnd

; Uninstaller section
Section "Uninstall"
    ; Remove files
    Delete "`$INSTDIR\`${APP_NAME}.exe"
    Delete "`$INSTDIR\logo.ico"
    Delete "`$INSTDIR\README.md"
    Delete "`$INSTDIR\uninstall.bat"
    Delete "`$INSTDIR\Uninstall.exe"
    
    ; Remove shortcuts
    Delete "`$SMPROGRAMS\`${APP_NAME}\`${APP_NAME}.lnk"
    Delete "`$SMPROGRAMS\`${APP_NAME}\Uninstall.lnk"
    Delete "`$DESKTOP\`${APP_NAME}.lnk"
    RMDir "`$SMPROGRAMS\`${APP_NAME}"
    
    ; Remove from startup
    DeleteRegValue HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "`${APP_NAME}"
    
    ; Remove registry entries
    DeleteRegKey HKCU "Software\`${APP_NAME}"
    DeleteRegKey HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}"
    
    ; Remove installation directory
    RMDir "`$INSTDIR"
SectionEnd
"@

$NSISScriptPath = Join-Path $InstallerDir "installer.nsi"
$NSISScript | Out-File -FilePath $NSISScriptPath -Encoding UTF8

Write-Step "`n📄 Step 4: Creating license file..."

# Create license file
$LicenseText = @"
TradingView Software License Agreement

This software is provided for educational and research purposes only.

TERMS AND CONDITIONS:

1. This software is provided "as is" without warranty of any kind.
2. The software is intended for educational purposes only.
3. Users are responsible for compliance with all applicable laws.
4. The publisher assumes no liability for any damages.

By installing this software, you agree to these terms.

Copyright (c) 2024 $Publisher
All rights reserved.
"@

$LicensePath = Join-Path $InstallerDir "license.txt"
$LicenseText | Out-File -FilePath $LicensePath -Encoding UTF8

Write-Step "`n🔧 Step 5: Building installer..."

# Check for NSIS
$NSISPath = @(
    "${env:ProgramFiles}\NSIS\makensis.exe",
    "${env:ProgramFiles(x86)}\NSIS\makensis.exe",
    "C:\Program Files\NSIS\makensis.exe",
    "C:\Program Files (x86)\NSIS\makensis.exe"
) | Where-Object { Test-Path $_ } | Select-Object -First 1

if ($NSISPath) {
    Write-Info "Found NSIS: $NSISPath"
    
    # Build installer
    Push-Location $InstallerDir
    try {
        if ($Verbose) {
            & $NSISPath "installer.nsi"
        } else {
            & $NSISPath "installer.nsi" 2>$null
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✓ NSIS installer created successfully!"
            
            # Check output
            $SetupFile = Join-Path $OutputDir "${AppName}_Setup_v${AppVersion}.exe"
            if (Test-Path $SetupFile) {
                $SetupSize = [math]::Round((Get-Item $SetupFile).Length / 1MB, 2)
                Write-Info "📁 Setup file: $SetupFile"
                Write-Info "📊 Size: $SetupSize MB"
            }
        } else {
            Write-Error "❌ NSIS build failed!"
        }
    } finally {
        Pop-Location
    }
} else {
    Write-Warning "⚠ NSIS not found. Creating simple self-extracting archive instead..."
    
    # Create simple batch installer as fallback
    $BatchInstaller = @"
@echo off
title TradingView Setup
echo Installing TradingView...

REM Create installation directory
if not exist "%LOCALAPPDATA%\TradingView" mkdir "%LOCALAPPDATA%\TradingView"

REM Copy files (this would be replaced with actual file extraction)
echo Extracting files...
copy "$AppName.exe" "%LOCALAPPDATA%\TradingView\" >nul
if exist "logo.ico" copy "logo.ico" "%LOCALAPPDATA%\TradingView\" >nul

REM Create shortcuts
echo Creating shortcuts...
powershell -Command "`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%USERPROFILE%\Desktop\TradingView.lnk'); `$Shortcut.TargetPath = '%LOCALAPPDATA%\TradingView\$AppName.exe'; `$Shortcut.IconLocation = '%LOCALAPPDATA%\TradingView\logo.ico'; `$Shortcut.Save()"

REM Add to startup
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "TradingView" /t REG_SZ /d "%LOCALAPPDATA%\TradingView\$AppName.exe" /f >nul

echo Installation completed successfully!
echo TradingView has been installed to: %LOCALAPPDATA%\TradingView
echo Desktop shortcut created.
echo Application will start automatically with Windows.

pause
"@
    
    $BatchInstallerPath = Join-Path $OutputDir "${AppName}_Setup.bat"
    $BatchInstaller | Out-File -FilePath $BatchInstallerPath -Encoding ASCII
    Write-Info "📁 Batch installer created: $BatchInstallerPath"
}

Write-Step "`n📋 Step 6: Creating portable version..."

# Create portable version
$PortableDir = Join-Path $OutputDir "TradingView_Portable"
if (Test-Path $PortableDir) { Remove-Item $PortableDir -Recurse -Force }
New-Item -ItemType Directory -Path $PortableDir | Out-Null

Copy-Item $ExePath $PortableDir -Force
if (Test-Path "logo.ico") { Copy-Item "logo.ico" $PortableDir -Force }

# Create portable launcher
$PortableLauncher = @"
@echo off
title TradingView Portable
cd /d "%~dp0"
start "" "$AppName.exe"
"@

$PortableLauncherPath = Join-Path $PortableDir "Run_TradingView.bat"
$PortableLauncher | Out-File -FilePath $PortableLauncherPath -Encoding ASCII

Write-Success "✓ Portable version created: $PortableDir"

Write-Success "`n🎉 All installers created successfully!"

Write-Step "`n📁 Output files:"
Get-ChildItem $OutputDir -Recurse | ForEach-Object {
    if (-not $_.PSIsContainer) {
        $size = "$([math]::Round($_.Length / 1KB, 1)) KB"
        $relativePath = $_.FullName.Replace($OutputDir, "").TrimStart('\')
        Write-Host "  📄 $relativePath ($size)" -ForegroundColor White
    }
}

Write-Host "`n🚀 Usage Instructions:" -ForegroundColor Yellow
Write-Host "• Double-click setup file to install" -ForegroundColor White
Write-Host "• Installer will create desktop shortcut" -ForegroundColor White
Write-Host "• Application will auto-start with Windows" -ForegroundColor White
Write-Host "• Use Control Panel to uninstall" -ForegroundColor White

Write-Host "`n⚠️  Important Notes:" -ForegroundColor Yellow
Write-Host "• Test installer in safe environment first" -ForegroundColor White
Write-Host "• Some antivirus may flag the installer" -ForegroundColor White
Write-Host "• For production, consider code signing" -ForegroundColor White

Read-Host "`nPress Enter to exit"
